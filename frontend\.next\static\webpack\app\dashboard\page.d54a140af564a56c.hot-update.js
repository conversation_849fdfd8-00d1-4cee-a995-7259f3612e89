"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./app/dashboard/page.tsx":
/*!********************************!*\
  !*** ./app/dashboard/page.tsx ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_ChevronLeft_ChevronRight_Home_LineChart_Loader2_LogOut_Menu_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,ChevronLeft,ChevronRight,Home,LineChart,Loader2,LogOut,Menu,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_ChevronLeft_ChevronRight_Home_LineChart_Loader2_LogOut_Menu_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,ChevronLeft,ChevronRight,Home,LineChart,Loader2,LogOut,Menu,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_ChevronLeft_ChevronRight_Home_LineChart_Loader2_LogOut_Menu_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,ChevronLeft,ChevronRight,Home,LineChart,Loader2,LogOut,Menu,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_ChevronLeft_ChevronRight_Home_LineChart_Loader2_LogOut_Menu_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,ChevronLeft,ChevronRight,Home,LineChart,Loader2,LogOut,Menu,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_ChevronLeft_ChevronRight_Home_LineChart_Loader2_LogOut_Menu_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,ChevronLeft,ChevronRight,Home,LineChart,Loader2,LogOut,Menu,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_ChevronLeft_ChevronRight_Home_LineChart_Loader2_LogOut_Menu_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,ChevronLeft,ChevronRight,Home,LineChart,Loader2,LogOut,Menu,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_ChevronLeft_ChevronRight_Home_LineChart_Loader2_LogOut_Menu_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,ChevronLeft,ChevronRight,Home,LineChart,Loader2,LogOut,Menu,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_ChevronLeft_ChevronRight_Home_LineChart_Loader2_LogOut_Menu_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,ChevronLeft,ChevronRight,Home,LineChart,Loader2,LogOut,Menu,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/chart-line.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_ChevronLeft_ChevronRight_Home_LineChart_Loader2_LogOut_Menu_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,ChevronLeft,ChevronRight,Home,LineChart,Loader2,LogOut,Menu,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_ChevronLeft_ChevronRight_Home_LineChart_Loader2_LogOut_Menu_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,ChevronLeft,ChevronRight,Home,LineChart,Loader2,LogOut,Menu,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_ChevronLeft_ChevronRight_Home_LineChart_Loader2_LogOut_Menu_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,ChevronLeft,ChevronRight,Home,LineChart,Loader2,LogOut,Menu,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_ChevronLeft_ChevronRight_Home_LineChart_Loader2_LogOut_Menu_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,ChevronLeft,ChevronRight,Home,LineChart,Loader2,LogOut,Menu,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_ChevronLeft_ChevronRight_Home_LineChart_Loader2_LogOut_Menu_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,ChevronLeft,ChevronRight,Home,LineChart,Loader2,LogOut,Menu,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_ChevronLeft_ChevronRight_Home_LineChart_Loader2_LogOut_Menu_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,ChevronLeft,ChevronRight,Home,LineChart,Loader2,LogOut,Menu,User,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n// Enhanced mock data for realistic demonstration\nconst agents = [\n    {\n        id: \"agent1\",\n        name: \"智能客服助手\",\n        url: \"https://example.com/agent1\",\n        type: \"客服\",\n        status: \"在线\",\n        lastActive: \"2分钟前\",\n        tasksCompleted: 127,\n        successRate: 98.5\n    },\n    {\n        id: \"agent2\",\n        name: \"数据分析专家\",\n        url: \"https://example.com/agent2\",\n        type: \"分析\",\n        status: \"忙碌\",\n        lastActive: \"刚刚\",\n        tasksCompleted: 89,\n        successRate: 96.2\n    },\n    {\n        id: \"agent3\",\n        name: \"内容创作助理\",\n        url: \"https://example.com/agent3\",\n        type: \"创作\",\n        status: \"在线\",\n        lastActive: \"5分钟前\",\n        tasksCompleted: 156,\n        successRate: 94.8\n    },\n    {\n        id: \"agent4\",\n        name: \"代码审查机器人\",\n        url: \"https://example.com/agent4\",\n        type: \"开发\",\n        status: \"离线\",\n        lastActive: \"1小时前\",\n        tasksCompleted: 73,\n        successRate: 99.1\n    }\n];\nconst dashboardStats = [\n    {\n        title: \"总代理\",\n        value: \"4\",\n        subtitle: \"3个在线\",\n        color: \"bg-blue-500\",\n        trend: \"+2\",\n        trendDirection: \"up\"\n    },\n    {\n        title: \"CPU使用率\",\n        value: \"42.8%\",\n        subtitle: \"平均负载\",\n        color: \"bg-green-500\",\n        trend: \"-5.2%\",\n        trendDirection: \"down\"\n    },\n    {\n        title: \"内存使用\",\n        value: \"67.3%\",\n        subtitle: \"8.2GB / 12GB\",\n        color: \"bg-purple-500\",\n        trend: \"+3.1%\",\n        trendDirection: \"up\"\n    },\n    {\n        title: \"今日任务\",\n        value: \"445\",\n        subtitle: \"已完成\",\n        color: \"bg-orange-500\",\n        trend: \"+28\",\n        trendDirection: \"up\"\n    }\n];\nconst agentActivity = [\n    {\n        id: 1,\n        agent: \"智能客服助手\",\n        action: \"处理用户咨询\",\n        status: \"已完成\",\n        time: \"刚刚\",\n        color: \"bg-green-500\",\n        duration: \"2分钟\"\n    },\n    {\n        id: 2,\n        agent: \"数据分析专家\",\n        action: \"生成销售报告\",\n        status: \"进行中\",\n        time: \"3分钟前\",\n        color: \"bg-blue-500\",\n        duration: \"预计5分钟\"\n    },\n    {\n        id: 3,\n        agent: \"内容创作助理\",\n        action: \"撰写产品描述\",\n        status: \"已完成\",\n        time: \"5分钟前\",\n        color: \"bg-green-500\",\n        duration: \"8分钟\"\n    },\n    {\n        id: 4,\n        agent: \"智能客服助手\",\n        action: \"更新知识库\",\n        status: \"已完成\",\n        time: \"8分钟前\",\n        color: \"bg-green-500\",\n        duration: \"3分钟\"\n    },\n    {\n        id: 5,\n        agent: \"代码审查机器人\",\n        action: \"代码质量检查\",\n        status: \"等待中\",\n        time: \"12分钟前\",\n        color: \"bg-yellow-500\",\n        duration: \"待处理\"\n    }\n];\n// Performance metrics for charts\nconst performanceData = {\n    systemLoad: [\n        {\n            time: \"00:00\",\n            cpu: 35,\n            memory: 62,\n            network: 45\n        },\n        {\n            time: \"04:00\",\n            cpu: 28,\n            memory: 58,\n            network: 38\n        },\n        {\n            time: \"08:00\",\n            cpu: 42,\n            memory: 65,\n            network: 52\n        },\n        {\n            time: \"12:00\",\n            cpu: 48,\n            memory: 71,\n            network: 61\n        },\n        {\n            time: \"16:00\",\n            cpu: 38,\n            memory: 67,\n            network: 47\n        },\n        {\n            time: \"20:00\",\n            cpu: 33,\n            memory: 63,\n            network: 42\n        }\n    ],\n    agentPerformance: [\n        {\n            name: \"智能客服助手\",\n            completed: 127,\n            success: 98.5,\n            avgTime: 3.2\n        },\n        {\n            name: \"数据分析专家\",\n            completed: 89,\n            success: 96.2,\n            avgTime: 12.5\n        },\n        {\n            name: \"内容创作助理\",\n            completed: 156,\n            success: 94.8,\n            avgTime: 8.7\n        },\n        {\n            name: \"代码审查机器人\",\n            completed: 73,\n            success: 99.1,\n            avgTime: 15.3\n        }\n    ],\n    taskDistribution: [\n        {\n            category: \"客服咨询\",\n            count: 185,\n            percentage: 41.6\n        },\n        {\n            category: \"数据分析\",\n            count: 89,\n            percentage: 20.0\n        },\n        {\n            category: \"内容创作\",\n            count: 156,\n            percentage: 35.1\n        },\n        {\n            category: \"代码审查\",\n            count: 15,\n            percentage: 3.3\n        }\n    ]\n};\nfunction DashboardPage() {\n    _s();\n    const [selectedView, setSelectedView] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"home\") // Changed from selectedAgent to selectedView for home/agent views\n    ;\n    const [username, setUsername] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [sidebarCollapsed, setSidebarCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mobileMenuOpen, setMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [touchStartX, setTouchStartX] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [touchCurrentX, setTouchCurrentX] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [iframeError, setIframeError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DashboardPage.useEffect\": ()=>{\n            const isLoggedIn = localStorage.getItem(\"isLoggedIn\");\n            const storedUsername = localStorage.getItem(\"username\");\n            if (!isLoggedIn) {\n                router.push(\"/\");\n                return;\n            }\n            if (storedUsername) {\n                setUsername(storedUsername);\n            }\n        }\n    }[\"DashboardPage.useEffect\"], [\n        router\n    ]);\n    // Enhanced mobile menu accessibility and keyboard support\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DashboardPage.useEffect\": ()=>{\n            const handleKeyDown = {\n                \"DashboardPage.useEffect.handleKeyDown\": (e)=>{\n                    if (e.key === 'Escape' && mobileMenuOpen) {\n                        setMobileMenuOpen(false);\n                    }\n                }\n            }[\"DashboardPage.useEffect.handleKeyDown\"];\n            const handleResize = {\n                \"DashboardPage.useEffect.handleResize\": ()=>{\n                    // Close mobile menu when switching to desktop view\n                    if (window.innerWidth >= 1024 && mobileMenuOpen) {\n                        setMobileMenuOpen(false);\n                    }\n                }\n            }[\"DashboardPage.useEffect.handleResize\"];\n            document.addEventListener('keydown', handleKeyDown);\n            window.addEventListener('resize', handleResize);\n            return ({\n                \"DashboardPage.useEffect\": ()=>{\n                    document.removeEventListener('keydown', handleKeyDown);\n                    window.removeEventListener('resize', handleResize);\n                }\n            })[\"DashboardPage.useEffect\"];\n        }\n    }[\"DashboardPage.useEffect\"], [\n        mobileMenuOpen\n    ]);\n    const handleLogout = ()=>{\n        localStorage.removeItem(\"isLoggedIn\");\n        localStorage.removeItem(\"username\");\n        router.push(\"/\");\n    };\n    const toggleSidebar = ()=>{\n        setSidebarCollapsed(!sidebarCollapsed);\n    };\n    const toggleMobileMenu = ()=>{\n        setMobileMenuOpen(!mobileMenuOpen);\n    };\n    // Enhanced touch handling for mobile menu\n    const handleTouchStart = (e)=>{\n        setTouchStartX(e.touches[0].clientX);\n        setTouchCurrentX(e.touches[0].clientX);\n    };\n    const handleTouchMove = (e)=>{\n        if (touchStartX === null) return;\n        setTouchCurrentX(e.touches[0].clientX);\n    };\n    const handleTouchEnd = ()=>{\n        if (touchStartX === null || touchCurrentX === null) return;\n        const deltaX = touchCurrentX - touchStartX;\n        const threshold = 50 // Minimum swipe distance\n        ;\n        // Swipe right to open menu (when closed)\n        if (deltaX > threshold && !mobileMenuOpen) {\n            setMobileMenuOpen(true);\n        } else if (deltaX < -threshold && mobileMenuOpen) {\n            setMobileMenuOpen(false);\n        }\n        setTouchStartX(null);\n        setTouchCurrentX(null);\n    };\n    // Close mobile menu when clicking outside\n    const handleBackdropClick = (e)=>{\n        e.stopPropagation();\n        setMobileMenuOpen(false);\n    };\n    const handleViewSelect = (view)=>{\n        // Renamed from handleAgentSelect to handleViewSelect\n        if (view === \"home\") {\n            setSelectedView(\"home\");\n            setMobileMenuOpen(false);\n            return;\n        }\n        setIsLoading(true);\n        setIframeError(false);\n        setSelectedView(view);\n        setMobileMenuOpen(false);\n        setTimeout(()=>setIsLoading(false), 1000);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-slate-50 flex\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"\\n          fixed inset-0 z-40 lg:hidden transition-all duration-300 ease-in-out\\n          \".concat(mobileMenuOpen ? \"bg-black bg-opacity-50 backdrop-blur-sm visible\" : \"bg-transparent invisible\", \"\\n        \"),\n                onClick: handleBackdropClick,\n                onTouchStart: handleTouchStart,\n                onTouchMove: handleTouchMove,\n                onTouchEnd: handleTouchEnd\n            }, void 0, false, {\n                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 296,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                className: \"\\n          \".concat(mobileMenuOpen ? \"translate-x-0 shadow-2xl\" : \"-translate-x-full shadow-none\", \"\\n          lg:translate-x-0 lg:shadow-none\\n          fixed lg:relative\\n          z-50 lg:z-auto\\n          bg-slate-900\\n          border-r border-slate-700\\n          flex flex-col\\n          transition-all duration-300 ease-out\\n          \").concat(sidebarCollapsed ? \"lg:w-16\" : \"lg:w-80\", \"\\n          w-80\\n          h-screen\\n          lg:transform-none\\n        \"),\n                onTouchStart: handleTouchStart,\n                onTouchMove: handleTouchMove,\n                onTouchEnd: handleTouchEnd,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"border-b border-slate-700/50 \".concat(sidebarCollapsed ? 'p-2' : 'p-3'),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center mb-3 \".concat(sidebarCollapsed ? 'justify-center' : 'justify-between'),\n                                children: [\n                                    !sidebarCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-lg font-semibold text-white\",\n                                        children: \"Agent 管理系统\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 331,\n                                        columnNumber: 35\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"sm\",\n                                                onClick: toggleSidebar,\n                                                className: \"hidden lg:flex text-slate-400 hover:bg-slate-800/80 hover:text-white rounded-lg transition-all duration-200\",\n                                                children: sidebarCollapsed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_ChevronLeft_ChevronRight_Home_LineChart_Loader2_LogOut_Menu_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 339,\n                                                    columnNumber: 37\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_ChevronLeft_ChevronRight_Home_LineChart_Loader2_LogOut_Menu_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 339,\n                                                    columnNumber: 76\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 333,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"sm\",\n                                                onClick: toggleMobileMenu,\n                                                className: \"lg:hidden text-slate-400 hover:bg-slate-800/80 hover:text-white rounded-lg transition-all duration-200 hover:scale-105 active:scale-95\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"transition-transform duration-200 hover:rotate-90\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_ChevronLeft_ChevronRight_Home_LineChart_Loader2_LogOut_Menu_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 348,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 347,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 341,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 332,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 330,\n                                columnNumber: 11\n                            }, this),\n                            sidebarCollapsed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center mb-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-7 h-7 bg-blue-600 rounded-lg flex items-center justify-center cursor-pointer hover:bg-blue-700 transition-all duration-200 hover:scale-105\",\n                                    title: \"\".concat(username || \"手系 Agent\", \" - 管理员\"),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_ChevronLeft_ChevronRight_Home_LineChart_Loader2_LogOut_Menu_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"h-3.5 w-3.5 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 361,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 357,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 356,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 p-2.5 bg-slate-800/60 rounded-lg mb-3 border border-slate-700/30\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-7 h-7 bg-blue-600 rounded-lg flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_ChevronLeft_ChevronRight_Home_LineChart_Loader2_LogOut_Menu_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                            className: \"h-3.5 w-3.5 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 367,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 366,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 min-w-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-white truncate\",\n                                                children: username || \"手系 Agent\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 370,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-slate-400\",\n                                                children: \"管理员\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 371,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 369,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 365,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 329,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"flex-1 \".concat(sidebarCollapsed ? 'p-2' : 'p-3', \" overflow-y-auto\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleViewSelect(\"home\"),\n                                        className: \"\\n                  w-full flex items-center rounded-lg text-sm font-medium\\n                  transition-all duration-200 min-h-[36px]\\n                  \".concat(sidebarCollapsed ? 'justify-center px-2 py-2.5' : 'gap-2.5 px-2.5 py-2.5', \"\\n                  \").concat(selectedView === \"home\" ? \"bg-blue-600 text-white shadow-md border border-blue-500/30\" : \"text-slate-300 hover:bg-slate-800/60 hover:text-white\", \"\\n                \"),\n                                        title: sidebarCollapsed ? \"首页\" : undefined,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_ChevronLeft_ChevronRight_Home_LineChart_Loader2_LogOut_Menu_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-4 w-4 flex-shrink-0\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 396,\n                                                columnNumber: 17\n                                            }, this),\n                                            !sidebarCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"首页\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 397,\n                                                columnNumber: 39\n                                            }, this),\n                                            !sidebarCollapsed && selectedView === \"home\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-auto w-1.5 h-1.5 bg-white rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 399,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 382,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 381,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"pt-3 relative group\",\n                                    children: sidebarCollapsed ? /* Collapsed: Show section icon with hover tooltip */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"\\n                      w-full flex items-center justify-center rounded-lg text-sm font-medium\\n                      transition-all duration-200 min-h-[36px] px-2 py-2.5 cursor-default\\n                      lg:hover:bg-slate-800/60 lg:hover:text-white\\n                      \".concat(agents.some((agent)=>selectedView === agent.id) ? \"bg-blue-600 text-white shadow-md border border-blue-500/30\" : \"text-slate-300\", \"\\n                    \"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_ChevronLeft_ChevronRight_Home_LineChart_Loader2_LogOut_Menu_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-4 w-4 flex-shrink-0\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 421,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 409,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"hidden lg:block absolute left-full top-0 ml-3 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 ease-out z-50 pointer-events-none group-hover:pointer-events-auto transform translate-x-1 group-hover:translate-x-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-slate-800/95 backdrop-blur-sm border border-slate-700/60 rounded-lg shadow-2xl min-w-[220px] py-2 before:content-[''] before:absolute before:left-[-6px] before:top-4 before:w-0 before:h-0 before:border-t-[6px] before:border-b-[6px] before:border-r-[6px] before:border-transparent before:border-r-slate-800/95\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"px-3 py-2 border-b border-slate-700/50\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-xs font-semibold text-slate-300 uppercase tracking-wider flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_ChevronLeft_ChevronRight_Home_LineChart_Loader2_LogOut_Menu_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                        className: \"h-3 w-3 text-blue-400\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 429,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    \"AI 专家\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 428,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 427,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"py-1\",\n                                                            children: agents.map((agent)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleViewSelect(agent.id),\n                                                                    className: \"\\n                              w-full flex items-center gap-2.5 px-3 py-2.5 text-sm font-medium\\n                              transition-all duration-200 hover:bg-slate-700/80 rounded-md mx-1\\n                              \".concat(selectedView === agent.id ? \"bg-blue-600 text-white shadow-md\" : \"text-slate-300 hover:text-white\", \"\\n                            \"),\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-2 h-2 rounded-full flex-shrink-0 \".concat(agent.status === '在线' ? 'bg-green-500' : agent.status === '忙碌' ? 'bg-yellow-500' : 'bg-gray-400')\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                            lineNumber: 448,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"truncate\",\n                                                                            children: agent.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                            lineNumber: 452,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        selectedView === agent.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"ml-auto w-1.5 h-1.5 bg-white rounded-full\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                            lineNumber: 454,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, agent.id, true, {\n                                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 435,\n                                                                    columnNumber: 27\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 433,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 426,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 425,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true) : /* Expanded: Show section header and all agent items */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"px-2.5 py-1.5 mb-1\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-xs font-semibold text-slate-400 uppercase tracking-wider flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_ChevronLeft_ChevronRight_Home_LineChart_Loader2_LogOut_Menu_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"h-3 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 467,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \"AI 专家\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 466,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 465,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-0.5\",\n                                                children: agents.map((agent)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleViewSelect(agent.id),\n                                                        className: \"\\n                          w-full flex items-center rounded-lg text-sm font-medium\\n                          transition-all duration-200 min-h-[34px] gap-2.5 px-3 py-2 ml-1\\n                          \".concat(selectedView === agent.id ? \"bg-blue-600 text-white shadow-md border border-blue-500/30\" : \"text-slate-300 hover:bg-slate-800/60 hover:text-white\", \"\\n                        \"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-2 h-2 rounded-full flex-shrink-0 \".concat(agent.status === '在线' ? 'bg-green-500' : agent.status === '忙碌' ? 'bg-yellow-500' : 'bg-gray-400')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 488,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"truncate\",\n                                                                children: agent.name\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 492,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            selectedView === agent.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"ml-auto w-1.5 h-1.5 bg-white rounded-full\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 494,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, agent.id, true, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 475,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 473,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 405,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"pt-3 relative group\",\n                                    children: sidebarCollapsed ? /* Collapsed: Show section icon with hover tooltip */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"\\n                      w-full flex items-center justify-center rounded-lg text-sm font-medium\\n                      transition-all duration-200 min-h-[36px] px-2 py-2.5 cursor-default\\n                      lg:hover:bg-slate-800/60 lg:hover:text-white\\n                      \".concat(selectedView === \"analytics\" ? \"bg-blue-600 text-white shadow-md border border-blue-500/30\" : \"text-slate-300\", \"\\n                    \"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_ChevronLeft_ChevronRight_Home_LineChart_Loader2_LogOut_Menu_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"h-4 w-4 flex-shrink-0\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 520,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 508,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"hidden lg:block absolute left-full top-0 ml-3 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 ease-out z-50 pointer-events-none group-hover:pointer-events-auto transform translate-x-1 group-hover:translate-x-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-slate-800/95 backdrop-blur-sm border border-slate-700/60 rounded-lg shadow-2xl min-w-[220px] py-2 before:content-[''] before:absolute before:left-[-6px] before:top-4 before:w-0 before:h-0 before:border-t-[6px] before:border-b-[6px] before:border-r-[6px] before:border-transparent before:border-r-slate-800/95\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"px-3 py-2 border-b border-slate-700/50\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-xs font-semibold text-slate-300 uppercase tracking-wider flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_ChevronLeft_ChevronRight_Home_LineChart_Loader2_LogOut_Menu_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        className: \"h-3 w-3 text-purple-400\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 528,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    \"分析工具\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 527,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 526,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"py-1\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>handleViewSelect(\"analytics\"),\n                                                                className: \"\\n                            w-full flex items-center gap-2.5 px-3 py-2.5 text-sm font-medium\\n                            transition-all duration-200 hover:bg-slate-700/80 rounded-md mx-1\\n                            \".concat(selectedView === \"analytics\" ? \"bg-blue-600 text-white shadow-md\" : \"text-slate-300 hover:text-white\", \"\\n                          \"),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_ChevronLeft_ChevronRight_Home_LineChart_Loader2_LogOut_Menu_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                        className: \"h-3.5 w-3.5 flex-shrink-0 text-purple-400\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 545,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"truncate\",\n                                                                        children: \"性能分析\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 546,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    selectedView === \"analytics\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"ml-auto w-1.5 h-1.5 bg-white rounded-full\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 548,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 533,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 532,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 525,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 524,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true) : /* Expanded: Show section header and analytics items */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"px-2.5 py-1.5 mb-1\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-xs font-semibold text-slate-400 uppercase tracking-wider flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_ChevronLeft_ChevronRight_Home_LineChart_Loader2_LogOut_Menu_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"h-3 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 560,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \"分析工具\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 559,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 558,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-0.5\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleViewSelect(\"analytics\"),\n                                                    className: \"\\n                        w-full flex items-center rounded-lg text-sm font-medium\\n                        transition-all duration-200 min-h-[34px] gap-2.5 px-3 py-2 ml-1\\n                        \".concat(selectedView === \"analytics\" ? \"bg-blue-600 text-white shadow-md border border-blue-500/30\" : \"text-slate-300 hover:bg-slate-800/60 hover:text-white\", \"\\n                      \"),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_ChevronLeft_ChevronRight_Home_LineChart_Loader2_LogOut_Menu_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                            className: \"h-3.5 w-3.5 flex-shrink-0\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 579,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"truncate\",\n                                                            children: \"性能分析\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 580,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        selectedView === \"analytics\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"ml-auto w-1.5 h-1.5 bg-white rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 582,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 567,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 566,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 504,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 379,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 377,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"border-t border-slate-700/50 \".concat(sidebarCollapsed ? 'p-2' : 'p-3'),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"ghost\",\n                            onClick: handleLogout,\n                            className: \"\\n              w-full text-slate-300 hover:bg-slate-800/60 hover:text-white rounded-lg\\n              transition-all duration-200 min-h-[36px]\\n              \".concat(sidebarCollapsed ? \"justify-center px-2 py-2\" : \"justify-start px-2.5 py-2\", \"\\n            \"),\n                            title: sidebarCollapsed ? \"退出登录\" : undefined,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_ChevronLeft_ChevronRight_Home_LineChart_Loader2_LogOut_Menu_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 603,\n                                    columnNumber: 13\n                                }, this),\n                                !sidebarCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"ml-2 text-sm\",\n                                    children: \"退出登录\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 604,\n                                    columnNumber: 35\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 593,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 592,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 310,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                variant: \"ghost\",\n                size: \"sm\",\n                onClick: toggleMobileMenu,\n                className: \"\\n          lg:hidden fixed top-3 left-3 z-30\\n          bg-slate-900/95 backdrop-blur-sm text-white\\n          hover:bg-slate-800/90 active:bg-slate-700\\n          border border-slate-700/40 rounded-lg\\n          transition-all duration-200 ease-in-out\\n          hover:scale-105 active:scale-95\\n          shadow-md hover:shadow-lg\\n          min-h-[36px] min-w-[36px]\\n          \".concat(mobileMenuOpen ? 'bg-slate-800/90 scale-105' : '', \"\\n        \"),\n                onTouchStart: handleTouchStart,\n                onTouchMove: handleTouchMove,\n                onTouchEnd: handleTouchEnd,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"transition-transform duration-200 \".concat(mobileMenuOpen ? 'rotate-90' : ''),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_ChevronLeft_ChevronRight_Home_LineChart_Loader2_LogOut_Menu_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 630,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 629,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 610,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"flex-1 p-3 lg:p-4 overflow-auto\",\n                children: selectedView === \"home\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg border border-slate-200 p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-xl font-bold text-slate-900 mb-1\",\n                                    children: [\n                                        \"欢迎回来，\",\n                                        username || \"管理员\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 639,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-slate-600\",\n                                    children: \"AI Agent 管理中心概览\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 640,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 638,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-3\",\n                            children: dashboardStats.map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                    className: \"bg-white border-slate-200 hover:shadow-lg transition-shadow\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                        className: \"p-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs font-medium text-slate-600\",\n                                                                children: stat.title\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 650,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-lg font-bold text-slate-900 mt-1\",\n                                                                children: stat.value\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 651,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-slate-500\",\n                                                                children: stat.subtitle\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 652,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 649,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-10 h-10 \".concat(stat.color, \" rounded-lg flex items-center justify-center\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-5 h-5 bg-white rounded opacity-80\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 655,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 654,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 648,\n                                                columnNumber: 21\n                                            }, this),\n                                            stat.trend && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-2 flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs font-medium \".concat(stat.trendDirection === 'up' ? 'text-green-600' : 'text-red-600'),\n                                                        children: [\n                                                            stat.trendDirection === 'up' ? '↗' : '↘',\n                                                            \" \",\n                                                            stat.trend\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 660,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-slate-500 ml-1\",\n                                                        children: \"vs 昨天\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 665,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 659,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 647,\n                                        columnNumber: 19\n                                    }, this)\n                                }, index, false, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 646,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 644,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                    className: \"bg-white border-slate-200 hover:shadow-lg transition-shadow\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                            className: \"pb-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                className: \"text-slate-900 flex items-center gap-2 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_ChevronLeft_ChevronRight_Home_LineChart_Loader2_LogOut_Menu_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                        className: \"h-4 w-4 text-blue-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 679,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Agent 性能\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 678,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 677,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                            className: \"space-y-2\",\n                                            children: performanceData.agentPerformance.slice(0, 3).map((agent, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between p-2 bg-slate-50 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1 min-w-0\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs font-medium text-slate-900 truncate\",\n                                                                    children: agent.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 687,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-slate-500\",\n                                                                    children: [\n                                                                        \"成功率: \",\n                                                                        agent.success,\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 688,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 686,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-right\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm font-bold text-blue-600\",\n                                                                    children: agent.completed\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 691,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-slate-500\",\n                                                                    children: \"任务\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 692,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 690,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 685,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 683,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 676,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                    className: \"bg-white border-slate-200 hover:shadow-lg transition-shadow\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                            className: \"pb-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                className: \"text-slate-900 flex items-center gap-2 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_ChevronLeft_ChevronRight_Home_LineChart_Loader2_LogOut_Menu_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"h-4 w-4 text-purple-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 703,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"系统负载\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 702,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 701,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                            className: \"space-y-2\",\n                                            children: performanceData.systemLoad.slice(-3).map((load, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between text-xs\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-slate-600\",\n                                                                    children: load.time\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 711,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-slate-900 font-medium\",\n                                                                    children: [\n                                                                        \"CPU: \",\n                                                                        load.cpu,\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 712,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 710,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-full bg-slate-200 rounded-full h-1.5\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-purple-600 h-1.5 rounded-full transition-all duration-300\",\n                                                                style: {\n                                                                    width: \"\".concat(load.cpu, \"%\")\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 715,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 714,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 709,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 707,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 700,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                    className: \"bg-white border-slate-200 md:col-span-2 xl:col-span-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                            className: \"pb-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                className: \"text-slate-900 flex items-center gap-2 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_ChevronLeft_ChevronRight_Home_LineChart_Loader2_LogOut_Menu_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"h-4 w-4 text-orange-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 729,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"最近活动\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 728,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 727,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                            className: \"space-y-2\",\n                                            children: agentActivity.slice(0, 3).map((activity)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2 p-2 bg-slate-50 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 rounded-full \".concat(activity.color)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 736,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1 min-w-0\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs font-medium text-slate-900 truncate\",\n                                                                    children: activity.agent\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 738,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-slate-600 truncate\",\n                                                                    children: activity.action\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 739,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 737,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-right\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs font-medium \".concat(activity.status === '已完成' ? 'text-green-600' : activity.status === '进行中' ? 'text-blue-600' : 'text-yellow-600'),\n                                                                children: activity.status\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 742,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 741,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, activity.id, true, {\n                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 735,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 733,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 726,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 674,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            className: \"bg-white border-slate-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                    className: \"pb-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                        className: \"text-slate-900 flex items-center gap-2 text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_ChevronLeft_ChevronRight_Home_LineChart_Loader2_LogOut_Menu_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"h-4 w-4 text-indigo-600\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 759,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Agent 状态总览\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 758,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 757,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-2\",\n                                        children: agents.map((agent)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between p-2 bg-slate-50 rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-2 h-2 rounded-full \".concat(agent.status === '在线' ? 'bg-green-500' : agent.status === '忙碌' ? 'bg-yellow-500' : 'bg-gray-400')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 768,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"min-w-0\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs font-medium text-slate-900 truncate\",\n                                                                        children: agent.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 773,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-slate-500\",\n                                                                        children: agent.type\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 774,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 772,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 767,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-right\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs font-medium text-slate-900\",\n                                                            children: agent.tasksCompleted\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 778,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 777,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, agent.id, true, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 766,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 764,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 763,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 756,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 636,\n                    columnNumber: 11\n                }, this) : (()=>{\n                    var _menuItems_find;\n                    // Handle agent views\n                    const selectedAgent = agents.find((agent)=>agent.id === selectedView);\n                    if (selectedAgent) {\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            className: \"h-full bg-white border-slate-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                className: \"h-full p-0\",\n                                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-full flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col items-center gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_ChevronLeft_ChevronRight_Home_LineChart_Loader2_LogOut_Menu_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"h-8 w-8 animate-spin text-blue-600\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 796,\n                                                columnNumber: 25\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-slate-600\",\n                                                children: [\n                                                    \"加载 \",\n                                                    selectedAgent.name,\n                                                    \"...\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 797,\n                                                columnNumber: 25\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 795,\n                                        columnNumber: 23\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 794,\n                                    columnNumber: 21\n                                }, this) : iframeError ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-full flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col items-center gap-4 text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_ChevronLeft_ChevronRight_Home_LineChart_Loader2_LogOut_Menu_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"h-12 w-12 text-red-500\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 803,\n                                                columnNumber: 25\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-slate-900 font-medium\",\n                                                        children: \"加载失败\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 805,\n                                                        columnNumber: 27\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-slate-600 text-sm mt-1\",\n                                                        children: [\n                                                            \"无法加载 \",\n                                                            selectedAgent.name\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 806,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 804,\n                                                columnNumber: 25\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                onClick: ()=>handleViewSelect(selectedView),\n                                                variant: \"outline\",\n                                                size: \"sm\",\n                                                children: \"重试\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 808,\n                                                columnNumber: 25\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 802,\n                                        columnNumber: 23\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 801,\n                                    columnNumber: 21\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"iframe\", {\n                                    src: selectedAgent.url,\n                                    className: \"w-full h-full border-0 rounded-lg\",\n                                    title: selectedAgent.name,\n                                    onError: ()=>setIframeError(true),\n                                    sandbox: \"allow-scripts allow-same-origin allow-forms\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 814,\n                                    columnNumber: 21\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 792,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 791,\n                            columnNumber: 15\n                        }, this);\n                    }\n                    // Handle other menu items\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                        className: \"h-full bg-white border-slate-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                            className: \"h-full p-0\",\n                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-full flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col items-center gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_ChevronLeft_ChevronRight_Home_LineChart_Loader2_LogOut_Menu_User_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"h-8 w-8 animate-spin text-blue-600\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 834,\n                                            columnNumber: 23\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-slate-600\",\n                                            children: \"加载中...\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 835,\n                                            columnNumber: 23\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 833,\n                                    columnNumber: 21\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 832,\n                                columnNumber: 19\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-full flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Package, {\n                                            className: \"h-16 w-16 text-slate-400 mx-auto mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 841,\n                                            columnNumber: 23\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-xl font-semibold text-slate-900 mb-2\",\n                                            children: ((_menuItems_find = menuItems.find((item)=>item.id === selectedView)) === null || _menuItems_find === void 0 ? void 0 : _menuItems_find.label) || \"功能\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 842,\n                                            columnNumber: 23\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-slate-600\",\n                                            children: \"此功能正在开发中...\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 845,\n                                            columnNumber: 23\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 840,\n                                    columnNumber: 21\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 839,\n                                columnNumber: 19\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 830,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 829,\n                        columnNumber: 13\n                    }, this);\n                })()\n            }, void 0, false, {\n                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 634,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 294,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardPage, \"LZ5QUMNVC8kN/tQl1hjuN5TcKSw=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = DashboardPage;\nvar _c;\n$RefreshReg$(_c, \"DashboardPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/dashboard/page.tsx\n"));

/***/ })

});