"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./app/dashboard/page.tsx":
/*!********************************!*\
  !*** ./app/dashboard/page.tsx ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,Calendar,ChevronLeft,ChevronRight,Container,Database,FileText,Globe,Home,LineChart,Loader2,LogOut,Menu,Package,Server,Settings,Terminal,User,Users,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,Calendar,ChevronLeft,ChevronRight,Container,Database,FileText,Globe,Home,LineChart,Loader2,LogOut,Menu,Package,Server,Settings,Terminal,User,Users,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,Calendar,ChevronLeft,ChevronRight,Container,Database,FileText,Globe,Home,LineChart,Loader2,LogOut,Menu,Package,Server,Settings,Terminal,User,Users,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,Calendar,ChevronLeft,ChevronRight,Container,Database,FileText,Globe,Home,LineChart,Loader2,LogOut,Menu,Package,Server,Settings,Terminal,User,Users,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,Calendar,ChevronLeft,ChevronRight,Container,Database,FileText,Globe,Home,LineChart,Loader2,LogOut,Menu,Package,Server,Settings,Terminal,User,Users,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,Calendar,ChevronLeft,ChevronRight,Container,Database,FileText,Globe,Home,LineChart,Loader2,LogOut,Menu,Package,Server,Settings,Terminal,User,Users,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/container.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,Calendar,ChevronLeft,ChevronRight,Container,Database,FileText,Globe,Home,LineChart,Loader2,LogOut,Menu,Package,Server,Settings,Terminal,User,Users,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/server.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,Calendar,ChevronLeft,ChevronRight,Container,Database,FileText,Globe,Home,LineChart,Loader2,LogOut,Menu,Package,Server,Settings,Terminal,User,Users,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/terminal.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,Calendar,ChevronLeft,ChevronRight,Container,Database,FileText,Globe,Home,LineChart,Loader2,LogOut,Menu,Package,Server,Settings,Terminal,User,Users,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,Calendar,ChevronLeft,ChevronRight,Container,Database,FileText,Globe,Home,LineChart,Loader2,LogOut,Menu,Package,Server,Settings,Terminal,User,Users,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/wrench.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,Calendar,ChevronLeft,ChevronRight,Container,Database,FileText,Globe,Home,LineChart,Loader2,LogOut,Menu,Package,Server,Settings,Terminal,User,Users,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,Calendar,ChevronLeft,ChevronRight,Container,Database,FileText,Globe,Home,LineChart,Loader2,LogOut,Menu,Package,Server,Settings,Terminal,User,Users,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,Calendar,ChevronLeft,ChevronRight,Container,Database,FileText,Globe,Home,LineChart,Loader2,LogOut,Menu,Package,Server,Settings,Terminal,User,Users,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,Calendar,ChevronLeft,ChevronRight,Container,Database,FileText,Globe,Home,LineChart,Loader2,LogOut,Menu,Package,Server,Settings,Terminal,User,Users,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,Calendar,ChevronLeft,ChevronRight,Container,Database,FileText,Globe,Home,LineChart,Loader2,LogOut,Menu,Package,Server,Settings,Terminal,User,Users,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,Calendar,ChevronLeft,ChevronRight,Container,Database,FileText,Globe,Home,LineChart,Loader2,LogOut,Menu,Package,Server,Settings,Terminal,User,Users,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,Calendar,ChevronLeft,ChevronRight,Container,Database,FileText,Globe,Home,LineChart,Loader2,LogOut,Menu,Package,Server,Settings,Terminal,User,Users,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,Calendar,ChevronLeft,ChevronRight,Container,Database,FileText,Globe,Home,LineChart,Loader2,LogOut,Menu,Package,Server,Settings,Terminal,User,Users,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/chart-line.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,Calendar,ChevronLeft,ChevronRight,Container,Database,FileText,Globe,Home,LineChart,Loader2,LogOut,Menu,Package,Server,Settings,Terminal,User,Users,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,Calendar,ChevronLeft,ChevronRight,Container,Database,FileText,Globe,Home,LineChart,Loader2,LogOut,Menu,Package,Server,Settings,Terminal,User,Users,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,Calendar,ChevronLeft,ChevronRight,Container,Database,FileText,Globe,Home,LineChart,Loader2,LogOut,Menu,Package,Server,Settings,Terminal,User,Users,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,Calendar,ChevronLeft,ChevronRight,Container,Database,FileText,Globe,Home,LineChart,Loader2,LogOut,Menu,Package,Server,Settings,Terminal,User,Users,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,Calendar,ChevronLeft,ChevronRight,Container,Database,FileText,Globe,Home,LineChart,Loader2,LogOut,Menu,Package,Server,Settings,Terminal,User,Users,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,Calendar,ChevronLeft,ChevronRight,Container,Database,FileText,Globe,Home,LineChart,Loader2,LogOut,Menu,Package,Server,Settings,Terminal,User,Users,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n// Menu configuration matching the reference design\nconst menuItems = [\n    {\n        id: \"home\",\n        icon: _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        label: \"首页\",\n        type: \"single\"\n    },\n    {\n        id: \"apps\",\n        icon: _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        label: \"应用\",\n        type: \"single\",\n        isActive: true // This represents the current active menu item\n    },\n    {\n        id: \"website\",\n        icon: _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        label: \"网站\",\n        type: \"group\",\n        children: [\n            {\n                id: \"website-1\",\n                label: \"网站管理\",\n                url: \"https://example.com/website1\"\n            },\n            {\n                id: \"website-2\",\n                label: \"域名管理\",\n                url: \"https://example.com/website2\"\n            },\n            {\n                id: \"website-3\",\n                label: \"SSL证书\",\n                url: \"https://example.com/website3\"\n            }\n        ]\n    },\n    {\n        id: \"ai\",\n        icon: _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        label: \"AI\",\n        type: \"group\",\n        children: [\n            {\n                id: \"ai-chat\",\n                label: \"智能对话\",\n                url: \"https://example.com/ai-chat\"\n            },\n            {\n                id: \"ai-analysis\",\n                label: \"数据分析\",\n                url: \"https://example.com/ai-analysis\"\n            },\n            {\n                id: \"ai-content\",\n                label: \"内容生成\",\n                url: \"https://example.com/ai-content\"\n            }\n        ]\n    },\n    {\n        id: \"database\",\n        icon: _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        label: \"数据库\",\n        type: \"single\"\n    },\n    {\n        id: \"docker\",\n        icon: _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        label: \"容器\",\n        type: \"single\"\n    },\n    {\n        id: \"system\",\n        icon: _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n        label: \"系统\",\n        type: \"group\",\n        children: [\n            {\n                id: \"system-monitor\",\n                label: \"系统监控\",\n                url: \"https://example.com/monitor\"\n            },\n            {\n                id: \"system-logs\",\n                label: \"系统日志\",\n                url: \"https://example.com/logs\"\n            },\n            {\n                id: \"system-users\",\n                label: \"用户管理\",\n                url: \"https://example.com/users\"\n            }\n        ]\n    },\n    {\n        id: \"terminal\",\n        icon: _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n        label: \"终端\",\n        type: \"single\"\n    },\n    {\n        id: \"schedule\",\n        icon: _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n        label: \"计划\",\n        type: \"single\"\n    },\n    {\n        id: \"tools\",\n        icon: _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n        label: \"工具\",\n        type: \"single\"\n    },\n    {\n        id: \"advanced\",\n        icon: _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        label: \"高级功能\",\n        type: \"group\",\n        children: [\n            {\n                id: \"advanced-backup\",\n                label: \"备份管理\",\n                url: \"https://example.com/backup\"\n            },\n            {\n                id: \"advanced-security\",\n                label: \"安全设置\",\n                url: \"https://example.com/security\"\n            },\n            {\n                id: \"advanced-api\",\n                label: \"API管理\",\n                url: \"https://example.com/api\"\n            }\n        ]\n    },\n    {\n        id: \"logs\",\n        icon: _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n        label: \"日志\",\n        type: \"single\"\n    },\n    {\n        id: \"settings\",\n        icon: _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n        label: \"设置\",\n        type: \"single\"\n    }\n];\n// Enhanced mock data for realistic demonstration\nconst agents = [\n    {\n        id: \"agent1\",\n        name: \"智能客服助手\",\n        url: \"https://example.com/agent1\",\n        type: \"客服\",\n        status: \"在线\",\n        lastActive: \"2分钟前\",\n        tasksCompleted: 127,\n        successRate: 98.5\n    },\n    {\n        id: \"agent2\",\n        name: \"数据分析专家\",\n        url: \"https://example.com/agent2\",\n        type: \"分析\",\n        status: \"忙碌\",\n        lastActive: \"刚刚\",\n        tasksCompleted: 89,\n        successRate: 96.2\n    },\n    {\n        id: \"agent3\",\n        name: \"内容创作助理\",\n        url: \"https://example.com/agent3\",\n        type: \"创作\",\n        status: \"在线\",\n        lastActive: \"5分钟前\",\n        tasksCompleted: 156,\n        successRate: 94.8\n    },\n    {\n        id: \"agent4\",\n        name: \"代码审查机器人\",\n        url: \"https://example.com/agent4\",\n        type: \"开发\",\n        status: \"离线\",\n        lastActive: \"1小时前\",\n        tasksCompleted: 73,\n        successRate: 99.1\n    }\n];\nconst dashboardStats = [\n    {\n        title: \"总代理\",\n        value: \"4\",\n        subtitle: \"3个在线\",\n        color: \"bg-blue-500\",\n        trend: \"+2\",\n        trendDirection: \"up\"\n    },\n    {\n        title: \"CPU使用率\",\n        value: \"42.8%\",\n        subtitle: \"平均负载\",\n        color: \"bg-green-500\",\n        trend: \"-5.2%\",\n        trendDirection: \"down\"\n    },\n    {\n        title: \"内存使用\",\n        value: \"67.3%\",\n        subtitle: \"8.2GB / 12GB\",\n        color: \"bg-purple-500\",\n        trend: \"+3.1%\",\n        trendDirection: \"up\"\n    },\n    {\n        title: \"今日任务\",\n        value: \"445\",\n        subtitle: \"已完成\",\n        color: \"bg-orange-500\",\n        trend: \"+28\",\n        trendDirection: \"up\"\n    }\n];\nconst agentActivity = [\n    {\n        id: 1,\n        agent: \"智能客服助手\",\n        action: \"处理用户咨询\",\n        status: \"已完成\",\n        time: \"刚刚\",\n        color: \"bg-green-500\",\n        duration: \"2分钟\"\n    },\n    {\n        id: 2,\n        agent: \"数据分析专家\",\n        action: \"生成销售报告\",\n        status: \"进行中\",\n        time: \"3分钟前\",\n        color: \"bg-blue-500\",\n        duration: \"预计5分钟\"\n    },\n    {\n        id: 3,\n        agent: \"内容创作助理\",\n        action: \"撰写产品描述\",\n        status: \"已完成\",\n        time: \"5分钟前\",\n        color: \"bg-green-500\",\n        duration: \"8分钟\"\n    },\n    {\n        id: 4,\n        agent: \"智能客服助手\",\n        action: \"更新知识库\",\n        status: \"已完成\",\n        time: \"8分钟前\",\n        color: \"bg-green-500\",\n        duration: \"3分钟\"\n    },\n    {\n        id: 5,\n        agent: \"代码审查机器人\",\n        action: \"代码质量检查\",\n        status: \"等待中\",\n        time: \"12分钟前\",\n        color: \"bg-yellow-500\",\n        duration: \"待处理\"\n    }\n];\n// Performance metrics for charts\nconst performanceData = {\n    systemLoad: [\n        {\n            time: \"00:00\",\n            cpu: 35,\n            memory: 62,\n            network: 45\n        },\n        {\n            time: \"04:00\",\n            cpu: 28,\n            memory: 58,\n            network: 38\n        },\n        {\n            time: \"08:00\",\n            cpu: 42,\n            memory: 65,\n            network: 52\n        },\n        {\n            time: \"12:00\",\n            cpu: 48,\n            memory: 71,\n            network: 61\n        },\n        {\n            time: \"16:00\",\n            cpu: 38,\n            memory: 67,\n            network: 47\n        },\n        {\n            time: \"20:00\",\n            cpu: 33,\n            memory: 63,\n            network: 42\n        }\n    ],\n    agentPerformance: [\n        {\n            name: \"智能客服助手\",\n            completed: 127,\n            success: 98.5,\n            avgTime: 3.2\n        },\n        {\n            name: \"数据分析专家\",\n            completed: 89,\n            success: 96.2,\n            avgTime: 12.5\n        },\n        {\n            name: \"内容创作助理\",\n            completed: 156,\n            success: 94.8,\n            avgTime: 8.7\n        },\n        {\n            name: \"代码审查机器人\",\n            completed: 73,\n            success: 99.1,\n            avgTime: 15.3\n        }\n    ],\n    taskDistribution: [\n        {\n            category: \"客服咨询\",\n            count: 185,\n            percentage: 41.6\n        },\n        {\n            category: \"数据分析\",\n            count: 89,\n            percentage: 20.0\n        },\n        {\n            category: \"内容创作\",\n            count: 156,\n            percentage: 35.1\n        },\n        {\n            category: \"代码审查\",\n            count: 15,\n            percentage: 3.3\n        }\n    ]\n};\nfunction DashboardPage() {\n    _s();\n    const [selectedView, setSelectedView] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"apps\") // Default to apps view to match reference\n    ;\n    const [username, setUsername] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [sidebarCollapsed, setSidebarCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true) // Default to collapsed like reference\n    ;\n    const [mobileMenuOpen, setMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [touchStartX, setTouchStartX] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [touchCurrentX, setTouchCurrentX] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [iframeError, setIframeError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [expandedGroups, setExpandedGroups] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set()) // Track expanded menu groups\n    ;\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DashboardPage.useEffect\": ()=>{\n            const isLoggedIn = localStorage.getItem(\"isLoggedIn\");\n            const storedUsername = localStorage.getItem(\"username\");\n            if (!isLoggedIn) {\n                router.push(\"/\");\n                return;\n            }\n            if (storedUsername) {\n                setUsername(storedUsername);\n            }\n        }\n    }[\"DashboardPage.useEffect\"], [\n        router\n    ]);\n    // Enhanced mobile menu accessibility and keyboard support\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DashboardPage.useEffect\": ()=>{\n            const handleKeyDown = {\n                \"DashboardPage.useEffect.handleKeyDown\": (e)=>{\n                    if (e.key === 'Escape' && mobileMenuOpen) {\n                        setMobileMenuOpen(false);\n                    }\n                }\n            }[\"DashboardPage.useEffect.handleKeyDown\"];\n            const handleResize = {\n                \"DashboardPage.useEffect.handleResize\": ()=>{\n                    // Close mobile menu when switching to desktop view\n                    if (window.innerWidth >= 1024 && mobileMenuOpen) {\n                        setMobileMenuOpen(false);\n                    }\n                }\n            }[\"DashboardPage.useEffect.handleResize\"];\n            document.addEventListener('keydown', handleKeyDown);\n            window.addEventListener('resize', handleResize);\n            return ({\n                \"DashboardPage.useEffect\": ()=>{\n                    document.removeEventListener('keydown', handleKeyDown);\n                    window.removeEventListener('resize', handleResize);\n                }\n            })[\"DashboardPage.useEffect\"];\n        }\n    }[\"DashboardPage.useEffect\"], [\n        mobileMenuOpen\n    ]);\n    const handleLogout = ()=>{\n        localStorage.removeItem(\"isLoggedIn\");\n        localStorage.removeItem(\"username\");\n        router.push(\"/\");\n    };\n    const toggleSidebar = ()=>{\n        setSidebarCollapsed(!sidebarCollapsed);\n    };\n    const toggleMobileMenu = ()=>{\n        setMobileMenuOpen(!mobileMenuOpen);\n    };\n    // Enhanced touch handling for mobile menu\n    const handleTouchStart = (e)=>{\n        setTouchStartX(e.touches[0].clientX);\n        setTouchCurrentX(e.touches[0].clientX);\n    };\n    const handleTouchMove = (e)=>{\n        if (touchStartX === null) return;\n        setTouchCurrentX(e.touches[0].clientX);\n    };\n    const handleTouchEnd = ()=>{\n        if (touchStartX === null || touchCurrentX === null) return;\n        const deltaX = touchCurrentX - touchStartX;\n        const threshold = 50 // Minimum swipe distance\n        ;\n        // Swipe right to open menu (when closed)\n        if (deltaX > threshold && !mobileMenuOpen) {\n            setMobileMenuOpen(true);\n        } else if (deltaX < -threshold && mobileMenuOpen) {\n            setMobileMenuOpen(false);\n        }\n        setTouchStartX(null);\n        setTouchCurrentX(null);\n    };\n    // Close mobile menu when clicking outside\n    const handleBackdropClick = (e)=>{\n        e.stopPropagation();\n        setMobileMenuOpen(false);\n    };\n    const handleViewSelect = (view)=>{\n        // Handle different view types\n        if (view === \"home\" || view === \"apps\") {\n            setSelectedView(view);\n            setMobileMenuOpen(false);\n            return;\n        }\n        // For other views, simulate loading\n        setIsLoading(true);\n        setIframeError(false);\n        setSelectedView(view);\n        setMobileMenuOpen(false);\n        setTimeout(()=>setIsLoading(false), 1000);\n    };\n    const handleMenuItemClick = (item)=>{\n        if (item.type === \"single\") {\n            handleViewSelect(item.id);\n        } else if (item.type === \"group\") {\n            // Toggle group expansion when sidebar is expanded\n            if (!sidebarCollapsed) {\n                setExpandedGroups((prev)=>{\n                    const newSet = new Set(prev);\n                    if (newSet.has(item.id)) {\n                        newSet.delete(item.id);\n                    } else {\n                        newSet.add(item.id);\n                    }\n                    return newSet;\n                });\n            }\n        }\n    };\n    const handleSubItemClick = (subItem)=>{\n        setIsLoading(true);\n        setIframeError(false);\n        setSelectedView(subItem.id);\n        setMobileMenuOpen(false);\n        setTimeout(()=>setIsLoading(false), 1000);\n    };\n    const selectedAgentData = agents.find((agent)=>agent.id === selectedView);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-slate-50 flex\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"\\n          fixed inset-0 z-40 lg:hidden transition-all duration-300 ease-in-out\\n          \".concat(mobileMenuOpen ? \"bg-black bg-opacity-50 backdrop-blur-sm visible\" : \"bg-transparent invisible\", \"\\n        \"),\n                onClick: handleBackdropClick,\n                onTouchStart: handleTouchStart,\n                onTouchMove: handleTouchMove,\n                onTouchEnd: handleTouchEnd\n            }, void 0, false, {\n                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 438,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                className: \"\\n          \".concat(mobileMenuOpen ? \"translate-x-0 shadow-2xl\" : \"-translate-x-full shadow-none\", \"\\n          lg:translate-x-0 lg:shadow-none\\n          fixed lg:relative\\n          z-50 lg:z-auto\\n          bg-slate-900\\n          border-r border-slate-700\\n          flex flex-col\\n          transition-all duration-300 ease-out\\n          \").concat(sidebarCollapsed ? \"lg:w-16\" : \"lg:w-80\", \"\\n          w-80\\n          h-screen\\n          lg:transform-none\\n        \"),\n                onTouchStart: handleTouchStart,\n                onTouchMove: handleTouchMove,\n                onTouchEnd: handleTouchEnd,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"border-b border-slate-700/50 \".concat(sidebarCollapsed ? 'p-2' : 'p-3'),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center mb-3 \".concat(sidebarCollapsed ? 'justify-center' : 'justify-between'),\n                                children: [\n                                    !sidebarCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-lg font-semibold text-white\",\n                                        children: \"Agent 管理系统\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 473,\n                                        columnNumber: 35\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"sm\",\n                                                onClick: toggleSidebar,\n                                                className: \"hidden lg:flex text-slate-400 hover:bg-slate-800/80 hover:text-white rounded-lg transition-all duration-200\",\n                                                children: sidebarCollapsed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 481,\n                                                    columnNumber: 37\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 481,\n                                                    columnNumber: 76\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 475,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"sm\",\n                                                onClick: toggleMobileMenu,\n                                                className: \"lg:hidden text-slate-400 hover:bg-slate-800/80 hover:text-white rounded-lg transition-all duration-200 hover:scale-105 active:scale-95\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"transition-transform duration-200 hover:rotate-90\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 490,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 489,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 483,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 474,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 472,\n                                columnNumber: 11\n                            }, this),\n                            sidebarCollapsed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center mb-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-7 h-7 bg-blue-600 rounded-lg flex items-center justify-center cursor-pointer hover:bg-blue-700 transition-all duration-200 hover:scale-105\",\n                                    title: \"\".concat(username || \"手系 Agent\", \" - 管理员\"),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        className: \"h-3.5 w-3.5 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 503,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 499,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 498,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 p-2.5 bg-slate-800/60 rounded-lg mb-3 border border-slate-700/30\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-7 h-7 bg-blue-600 rounded-lg flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                            className: \"h-3.5 w-3.5 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 509,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 508,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 min-w-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-white truncate\",\n                                                children: username || \"手系 Agent\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 512,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-slate-400\",\n                                                children: \"管理员\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 513,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 511,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 507,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 471,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"flex-1 \".concat(sidebarCollapsed ? 'p-2' : 'p-3', \" overflow-y-auto\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleViewSelect(\"home\"),\n                                        className: \"\\n                  w-full flex items-center rounded-lg text-sm font-medium\\n                  transition-all duration-200 min-h-[36px]\\n                  \".concat(sidebarCollapsed ? 'justify-center px-2 py-2.5' : 'gap-2.5 px-2.5 py-2.5', \"\\n                  \").concat(selectedView === \"home\" ? \"bg-blue-600 text-white shadow-md border border-blue-500/30\" : \"text-slate-300 hover:bg-slate-800/60 hover:text-white\", \"\\n                \"),\n                                        title: sidebarCollapsed ? \"首页\" : undefined,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"h-4 w-4 flex-shrink-0\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 538,\n                                                columnNumber: 17\n                                            }, this),\n                                            !sidebarCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"首页\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 539,\n                                                columnNumber: 39\n                                            }, this),\n                                            !sidebarCollapsed && selectedView === \"home\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-auto w-1.5 h-1.5 bg-white rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 541,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 524,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 523,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"pt-3 relative group\",\n                                    children: sidebarCollapsed ? /* Collapsed: Show section icon with hover tooltip */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"\\n                      w-full flex items-center justify-center rounded-lg text-sm font-medium\\n                      transition-all duration-200 min-h-[36px] px-2 py-2.5 cursor-default\\n                      lg:hover:bg-slate-800/60 lg:hover:text-white\\n                      \".concat(agents.some((agent)=>selectedView === agent.id) ? \"bg-blue-600 text-white shadow-md border border-blue-500/30\" : \"text-slate-300\", \"\\n                    \"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"h-4 w-4 flex-shrink-0\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 563,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 551,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"hidden lg:block absolute left-full top-0 ml-3 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 ease-out z-50 pointer-events-none group-hover:pointer-events-auto transform translate-x-1 group-hover:translate-x-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-slate-800/95 backdrop-blur-sm border border-slate-700/60 rounded-lg shadow-2xl min-w-[220px] py-2 before:content-[''] before:absolute before:left-[-6px] before:top-4 before:w-0 before:h-0 before:border-t-[6px] before:border-b-[6px] before:border-r-[6px] before:border-transparent before:border-r-slate-800/95\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"px-3 py-2 border-b border-slate-700/50\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-xs font-semibold text-slate-300 uppercase tracking-wider flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                        className: \"h-3 w-3 text-blue-400\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 571,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    \"AI 专家\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 570,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 569,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"py-1\",\n                                                            children: agents.map((agent)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleViewSelect(agent.id),\n                                                                    className: \"\\n                              w-full flex items-center gap-2.5 px-3 py-2.5 text-sm font-medium\\n                              transition-all duration-200 hover:bg-slate-700/80 rounded-md mx-1\\n                              \".concat(selectedView === agent.id ? \"bg-blue-600 text-white shadow-md\" : \"text-slate-300 hover:text-white\", \"\\n                            \"),\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-2 h-2 rounded-full flex-shrink-0 \".concat(agent.status === '在线' ? 'bg-green-500' : agent.status === '忙碌' ? 'bg-yellow-500' : 'bg-gray-400')\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                            lineNumber: 590,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"truncate\",\n                                                                            children: agent.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                            lineNumber: 594,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        selectedView === agent.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"ml-auto w-1.5 h-1.5 bg-white rounded-full\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                            lineNumber: 596,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, agent.id, true, {\n                                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 577,\n                                                                    columnNumber: 27\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 575,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 568,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 567,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true) : /* Expanded: Show section header and all agent items */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"px-2.5 py-1.5 mb-1\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-xs font-semibold text-slate-400 uppercase tracking-wider flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            className: \"h-3 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 609,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \"AI 专家\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 608,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 607,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-0.5\",\n                                                children: agents.map((agent)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleViewSelect(agent.id),\n                                                        className: \"\\n                          w-full flex items-center rounded-lg text-sm font-medium\\n                          transition-all duration-200 min-h-[34px] gap-2.5 px-3 py-2 ml-1\\n                          \".concat(selectedView === agent.id ? \"bg-blue-600 text-white shadow-md border border-blue-500/30\" : \"text-slate-300 hover:bg-slate-800/60 hover:text-white\", \"\\n                        \"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-2 h-2 rounded-full flex-shrink-0 \".concat(agent.status === '在线' ? 'bg-green-500' : agent.status === '忙碌' ? 'bg-yellow-500' : 'bg-gray-400')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 630,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"truncate\",\n                                                                children: agent.name\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 634,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            selectedView === agent.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"ml-auto w-1.5 h-1.5 bg-white rounded-full\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 636,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, agent.id, true, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 617,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 615,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 547,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"pt-3 relative group\",\n                                    children: sidebarCollapsed ? /* Collapsed: Show section icon with hover tooltip */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"\\n                      w-full flex items-center justify-center rounded-lg text-sm font-medium\\n                      transition-all duration-200 min-h-[36px] px-2 py-2.5 cursor-default\\n                      lg:hover:bg-slate-800/60 lg:hover:text-white\\n                      \".concat(selectedView === \"analytics\" ? \"bg-blue-600 text-white shadow-md border border-blue-500/30\" : \"text-slate-300\", \"\\n                    \"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                    className: \"h-4 w-4 flex-shrink-0\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 662,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 650,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"hidden lg:block absolute left-full top-0 ml-3 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 ease-out z-50 pointer-events-none group-hover:pointer-events-auto transform translate-x-1 group-hover:translate-x-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-slate-800/95 backdrop-blur-sm border border-slate-700/60 rounded-lg shadow-2xl min-w-[220px] py-2 before:content-[''] before:absolute before:left-[-6px] before:top-4 before:w-0 before:h-0 before:border-t-[6px] before:border-b-[6px] before:border-r-[6px] before:border-transparent before:border-r-slate-800/95\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"px-3 py-2 border-b border-slate-700/50\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-xs font-semibold text-slate-300 uppercase tracking-wider flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                        className: \"h-3 w-3 text-purple-400\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 670,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    \"分析工具\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 669,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 668,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"py-1\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>handleViewSelect(\"analytics\"),\n                                                                className: \"\\n                            w-full flex items-center gap-2.5 px-3 py-2.5 text-sm font-medium\\n                            transition-all duration-200 hover:bg-slate-700/80 rounded-md mx-1\\n                            \".concat(selectedView === \"analytics\" ? \"bg-blue-600 text-white shadow-md\" : \"text-slate-300 hover:text-white\", \"\\n                          \"),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                        className: \"h-3.5 w-3.5 flex-shrink-0 text-purple-400\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 687,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"truncate\",\n                                                                        children: \"性能分析\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 688,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    selectedView === \"analytics\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"ml-auto w-1.5 h-1.5 bg-white rounded-full\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 690,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 675,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 674,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 667,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 666,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true) : /* Expanded: Show section header and analytics items */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"px-2.5 py-1.5 mb-1\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-xs font-semibold text-slate-400 uppercase tracking-wider flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                            className: \"h-3 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 702,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \"分析工具\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 701,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 700,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-0.5\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleViewSelect(\"analytics\"),\n                                                    className: \"\\n                        w-full flex items-center rounded-lg text-sm font-medium\\n                        transition-all duration-200 min-h-[34px] gap-2.5 px-3 py-2 ml-1\\n                        \".concat(selectedView === \"analytics\" ? \"bg-blue-600 text-white shadow-md border border-blue-500/30\" : \"text-slate-300 hover:bg-slate-800/60 hover:text-white\", \"\\n                      \"),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                            className: \"h-3.5 w-3.5 flex-shrink-0\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 721,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"truncate\",\n                                                            children: \"性能分析\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 722,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        selectedView === \"analytics\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"ml-auto w-1.5 h-1.5 bg-white rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 724,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 709,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 708,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 646,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 521,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 519,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"border-t border-slate-700/50 \".concat(sidebarCollapsed ? 'p-2' : 'p-3'),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"ghost\",\n                            onClick: handleLogout,\n                            className: \"\\n              w-full text-slate-300 hover:bg-slate-800/60 hover:text-white rounded-lg\\n              transition-all duration-200 min-h-[36px]\\n              \".concat(sidebarCollapsed ? \"justify-center px-2 py-2\" : \"justify-start px-2.5 py-2\", \"\\n            \"),\n                            title: sidebarCollapsed ? \"退出登录\" : undefined,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 745,\n                                    columnNumber: 13\n                                }, this),\n                                !sidebarCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"ml-2 text-sm\",\n                                    children: \"退出登录\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 746,\n                                    columnNumber: 35\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 735,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 734,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 452,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                variant: \"ghost\",\n                size: \"sm\",\n                onClick: toggleMobileMenu,\n                className: \"\\n          lg:hidden fixed top-3 left-3 z-30\\n          bg-slate-900/95 backdrop-blur-sm text-white\\n          hover:bg-slate-800/90 active:bg-slate-700\\n          border border-slate-700/40 rounded-lg\\n          transition-all duration-200 ease-in-out\\n          hover:scale-105 active:scale-95\\n          shadow-md hover:shadow-lg\\n          min-h-[36px] min-w-[36px]\\n          \".concat(mobileMenuOpen ? 'bg-slate-800/90 scale-105' : '', \"\\n        \"),\n                onTouchStart: handleTouchStart,\n                onTouchMove: handleTouchMove,\n                onTouchEnd: handleTouchEnd,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"transition-transform duration-200 \".concat(mobileMenuOpen ? 'rotate-90' : ''),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 772,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 771,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 752,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"flex-1 p-3 lg:p-4 overflow-auto\",\n                children: selectedView === \"home\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg border border-slate-200 p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-xl font-bold text-slate-900 mb-1\",\n                                    children: [\n                                        \"欢迎回来，\",\n                                        username || \"管理员\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 781,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-slate-600\",\n                                    children: \"AI Agent 管理中心概览\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 782,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 780,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-3\",\n                            children: dashboardStats.map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                    className: \"bg-white border-slate-200 hover:shadow-lg transition-shadow\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                        className: \"p-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs font-medium text-slate-600\",\n                                                                children: stat.title\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 792,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-lg font-bold text-slate-900 mt-1\",\n                                                                children: stat.value\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 793,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-slate-500\",\n                                                                children: stat.subtitle\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 794,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 791,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-10 h-10 \".concat(stat.color, \" rounded-lg flex items-center justify-center\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-5 h-5 bg-white rounded opacity-80\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 797,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 796,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 790,\n                                                columnNumber: 21\n                                            }, this),\n                                            stat.trend && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-2 flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs font-medium \".concat(stat.trendDirection === 'up' ? 'text-green-600' : 'text-red-600'),\n                                                        children: [\n                                                            stat.trendDirection === 'up' ? '↗' : '↘',\n                                                            \" \",\n                                                            stat.trend\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 802,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-slate-500 ml-1\",\n                                                        children: \"vs 昨天\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 807,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 801,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 789,\n                                        columnNumber: 19\n                                    }, this)\n                                }, index, false, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 788,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 786,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                    className: \"bg-white border-slate-200 hover:shadow-lg transition-shadow\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                            className: \"pb-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                className: \"text-slate-900 flex items-center gap-2 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        className: \"h-4 w-4 text-blue-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 821,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Agent 性能\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 820,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 819,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                            className: \"space-y-2\",\n                                            children: performanceData.agentPerformance.slice(0, 3).map((agent, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between p-2 bg-slate-50 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1 min-w-0\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs font-medium text-slate-900 truncate\",\n                                                                    children: agent.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 829,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-slate-500\",\n                                                                    children: [\n                                                                        \"成功率: \",\n                                                                        agent.success,\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 830,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 828,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-right\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm font-bold text-blue-600\",\n                                                                    children: agent.completed\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 833,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-slate-500\",\n                                                                    children: \"任务\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 834,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 832,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 827,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 825,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 818,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                    className: \"bg-white border-slate-200 hover:shadow-lg transition-shadow\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                            className: \"pb-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                className: \"text-slate-900 flex items-center gap-2 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                        className: \"h-4 w-4 text-purple-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 845,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"系统负载\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 844,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 843,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                            className: \"space-y-2\",\n                                            children: performanceData.systemLoad.slice(-3).map((load, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between text-xs\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-slate-600\",\n                                                                    children: load.time\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 853,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-slate-900 font-medium\",\n                                                                    children: [\n                                                                        \"CPU: \",\n                                                                        load.cpu,\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 854,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 852,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-full bg-slate-200 rounded-full h-1.5\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-purple-600 h-1.5 rounded-full transition-all duration-300\",\n                                                                style: {\n                                                                    width: \"\".concat(load.cpu, \"%\")\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 857,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 856,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 851,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 849,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 842,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                    className: \"bg-white border-slate-200 md:col-span-2 xl:col-span-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                            className: \"pb-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                className: \"text-slate-900 flex items-center gap-2 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                        className: \"h-4 w-4 text-orange-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 871,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"最近活动\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 870,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 869,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                            className: \"space-y-2\",\n                                            children: agentActivity.slice(0, 3).map((activity)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2 p-2 bg-slate-50 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 rounded-full \".concat(activity.color)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 878,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1 min-w-0\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs font-medium text-slate-900 truncate\",\n                                                                    children: activity.agent\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 880,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-slate-600 truncate\",\n                                                                    children: activity.action\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 881,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 879,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-right\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs font-medium \".concat(activity.status === '已完成' ? 'text-green-600' : activity.status === '进行中' ? 'text-blue-600' : 'text-yellow-600'),\n                                                                children: activity.status\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 884,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 883,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, activity.id, true, {\n                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 877,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 875,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 868,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 816,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            className: \"bg-white border-slate-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                    className: \"pb-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                        className: \"text-slate-900 flex items-center gap-2 text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                className: \"h-4 w-4 text-indigo-600\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 901,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Agent 状态总览\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 900,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 899,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-2\",\n                                        children: agents.map((agent)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between p-2 bg-slate-50 rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-2 h-2 rounded-full \".concat(agent.status === '在线' ? 'bg-green-500' : agent.status === '忙碌' ? 'bg-yellow-500' : 'bg-gray-400')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 910,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"min-w-0\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs font-medium text-slate-900 truncate\",\n                                                                        children: agent.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 915,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-slate-500\",\n                                                                        children: agent.type\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 916,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 914,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 909,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-right\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs font-medium text-slate-900\",\n                                                            children: agent.tasksCompleted\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 920,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 919,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, agent.id, true, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 908,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 906,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 905,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 898,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 778,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                    className: \"h-full bg-white border-slate-200\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        className: \"h-full p-0\",\n                        children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-full flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                        className: \"h-8 w-8 animate-spin text-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 934,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-slate-600\",\n                                        children: [\n                                            \"加载 \",\n                                            selectedAgentData === null || selectedAgentData === void 0 ? void 0 : selectedAgentData.name,\n                                            \"...\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 935,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 933,\n                                columnNumber: 19\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 932,\n                            columnNumber: 17\n                        }, this) : iframeError ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-full flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center gap-4 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                        className: \"h-12 w-12 text-red-500\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 941,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-slate-900 font-medium\",\n                                                children: \"加载失败\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 943,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-slate-600 text-sm mt-1\",\n                                                children: [\n                                                    \"无法加载 \",\n                                                    selectedAgentData === null || selectedAgentData === void 0 ? void 0 : selectedAgentData.name\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 944,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 942,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        onClick: ()=>handleViewSelect(selectedView),\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        children: \"重试\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 946,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 940,\n                                columnNumber: 19\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 939,\n                            columnNumber: 17\n                        }, this) : selectedAgentData ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"iframe\", {\n                            src: selectedAgentData.url,\n                            className: \"w-full h-full border-0 rounded-lg\",\n                            title: selectedAgentData.name,\n                            onError: ()=>setIframeError(true),\n                            sandbox: \"allow-scripts allow-same-origin allow-forms\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 952,\n                            columnNumber: 17\n                        }, this) : null\n                    }, void 0, false, {\n                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 930,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 929,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 776,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 436,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardPage, \"WP+xSObwB6TJowbL3Zc/sms9bro=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = DashboardPage;\nvar _c;\n$RefreshReg$(_c, \"DashboardPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/dashboard/page.tsx\n"));

/***/ })

});