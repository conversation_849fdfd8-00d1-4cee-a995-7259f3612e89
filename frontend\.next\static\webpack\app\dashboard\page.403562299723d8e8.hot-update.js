"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./app/dashboard/page.tsx":
/*!********************************!*\
  !*** ./app/dashboard/page.tsx ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,Calendar,ChevronLeft,ChevronRight,Container,Database,FileText,Globe,Home,LineChart,Loader2,LogOut,Menu,Package,Server,Settings,Terminal,User,Users,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,Calendar,ChevronLeft,ChevronRight,Container,Database,FileText,Globe,Home,LineChart,Loader2,LogOut,Menu,Package,Server,Settings,Terminal,User,Users,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,Calendar,ChevronLeft,ChevronRight,Container,Database,FileText,Globe,Home,LineChart,Loader2,LogOut,Menu,Package,Server,Settings,Terminal,User,Users,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,Calendar,ChevronLeft,ChevronRight,Container,Database,FileText,Globe,Home,LineChart,Loader2,LogOut,Menu,Package,Server,Settings,Terminal,User,Users,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,Calendar,ChevronLeft,ChevronRight,Container,Database,FileText,Globe,Home,LineChart,Loader2,LogOut,Menu,Package,Server,Settings,Terminal,User,Users,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,Calendar,ChevronLeft,ChevronRight,Container,Database,FileText,Globe,Home,LineChart,Loader2,LogOut,Menu,Package,Server,Settings,Terminal,User,Users,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/container.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,Calendar,ChevronLeft,ChevronRight,Container,Database,FileText,Globe,Home,LineChart,Loader2,LogOut,Menu,Package,Server,Settings,Terminal,User,Users,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/server.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,Calendar,ChevronLeft,ChevronRight,Container,Database,FileText,Globe,Home,LineChart,Loader2,LogOut,Menu,Package,Server,Settings,Terminal,User,Users,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/terminal.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,Calendar,ChevronLeft,ChevronRight,Container,Database,FileText,Globe,Home,LineChart,Loader2,LogOut,Menu,Package,Server,Settings,Terminal,User,Users,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,Calendar,ChevronLeft,ChevronRight,Container,Database,FileText,Globe,Home,LineChart,Loader2,LogOut,Menu,Package,Server,Settings,Terminal,User,Users,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/wrench.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,Calendar,ChevronLeft,ChevronRight,Container,Database,FileText,Globe,Home,LineChart,Loader2,LogOut,Menu,Package,Server,Settings,Terminal,User,Users,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,Calendar,ChevronLeft,ChevronRight,Container,Database,FileText,Globe,Home,LineChart,Loader2,LogOut,Menu,Package,Server,Settings,Terminal,User,Users,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,Calendar,ChevronLeft,ChevronRight,Container,Database,FileText,Globe,Home,LineChart,Loader2,LogOut,Menu,Package,Server,Settings,Terminal,User,Users,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,Calendar,ChevronLeft,ChevronRight,Container,Database,FileText,Globe,Home,LineChart,Loader2,LogOut,Menu,Package,Server,Settings,Terminal,User,Users,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,Calendar,ChevronLeft,ChevronRight,Container,Database,FileText,Globe,Home,LineChart,Loader2,LogOut,Menu,Package,Server,Settings,Terminal,User,Users,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,Calendar,ChevronLeft,ChevronRight,Container,Database,FileText,Globe,Home,LineChart,Loader2,LogOut,Menu,Package,Server,Settings,Terminal,User,Users,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,Calendar,ChevronLeft,ChevronRight,Container,Database,FileText,Globe,Home,LineChart,Loader2,LogOut,Menu,Package,Server,Settings,Terminal,User,Users,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,Calendar,ChevronLeft,ChevronRight,Container,Database,FileText,Globe,Home,LineChart,Loader2,LogOut,Menu,Package,Server,Settings,Terminal,User,Users,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/chart-line.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,Calendar,ChevronLeft,ChevronRight,Container,Database,FileText,Globe,Home,LineChart,Loader2,LogOut,Menu,Package,Server,Settings,Terminal,User,Users,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,Calendar,ChevronLeft,ChevronRight,Container,Database,FileText,Globe,Home,LineChart,Loader2,LogOut,Menu,Package,Server,Settings,Terminal,User,Users,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,Calendar,ChevronLeft,ChevronRight,Container,Database,FileText,Globe,Home,LineChart,Loader2,LogOut,Menu,Package,Server,Settings,Terminal,User,Users,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,Calendar,ChevronLeft,ChevronRight,Container,Database,FileText,Globe,Home,LineChart,Loader2,LogOut,Menu,Package,Server,Settings,Terminal,User,Users,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,Calendar,ChevronLeft,ChevronRight,Container,Database,FileText,Globe,Home,LineChart,Loader2,LogOut,Menu,Package,Server,Settings,Terminal,User,Users,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,Calendar,ChevronLeft,ChevronRight,Container,Database,FileText,Globe,Home,LineChart,Loader2,LogOut,Menu,Package,Server,Settings,Terminal,User,Users,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n// Menu configuration matching the reference design\nconst menuItems = [\n    {\n        id: \"home\",\n        icon: _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        label: \"首页\",\n        type: \"single\"\n    },\n    {\n        id: \"apps\",\n        icon: _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        label: \"应用\",\n        type: \"single\",\n        isActive: true // This represents the current active menu item\n    },\n    {\n        id: \"website\",\n        icon: _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        label: \"网站\",\n        type: \"group\",\n        children: [\n            {\n                id: \"website-1\",\n                label: \"网站管理\",\n                url: \"https://example.com/website1\"\n            },\n            {\n                id: \"website-2\",\n                label: \"域名管理\",\n                url: \"https://example.com/website2\"\n            },\n            {\n                id: \"website-3\",\n                label: \"SSL证书\",\n                url: \"https://example.com/website3\"\n            }\n        ]\n    },\n    {\n        id: \"ai\",\n        icon: _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        label: \"AI\",\n        type: \"group\",\n        children: [\n            {\n                id: \"ai-chat\",\n                label: \"智能对话\",\n                url: \"https://example.com/ai-chat\"\n            },\n            {\n                id: \"ai-analysis\",\n                label: \"数据分析\",\n                url: \"https://example.com/ai-analysis\"\n            },\n            {\n                id: \"ai-content\",\n                label: \"内容生成\",\n                url: \"https://example.com/ai-content\"\n            }\n        ]\n    },\n    {\n        id: \"database\",\n        icon: _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        label: \"数据库\",\n        type: \"single\"\n    },\n    {\n        id: \"docker\",\n        icon: _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        label: \"容器\",\n        type: \"single\"\n    },\n    {\n        id: \"system\",\n        icon: _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n        label: \"系统\",\n        type: \"group\",\n        children: [\n            {\n                id: \"system-monitor\",\n                label: \"系统监控\",\n                url: \"https://example.com/monitor\"\n            },\n            {\n                id: \"system-logs\",\n                label: \"系统日志\",\n                url: \"https://example.com/logs\"\n            },\n            {\n                id: \"system-users\",\n                label: \"用户管理\",\n                url: \"https://example.com/users\"\n            }\n        ]\n    },\n    {\n        id: \"terminal\",\n        icon: _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n        label: \"终端\",\n        type: \"single\"\n    },\n    {\n        id: \"schedule\",\n        icon: _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n        label: \"计划\",\n        type: \"single\"\n    },\n    {\n        id: \"tools\",\n        icon: _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n        label: \"工具\",\n        type: \"single\"\n    },\n    {\n        id: \"advanced\",\n        icon: _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        label: \"高级功能\",\n        type: \"group\",\n        children: [\n            {\n                id: \"advanced-backup\",\n                label: \"备份管理\",\n                url: \"https://example.com/backup\"\n            },\n            {\n                id: \"advanced-security\",\n                label: \"安全设置\",\n                url: \"https://example.com/security\"\n            },\n            {\n                id: \"advanced-api\",\n                label: \"API管理\",\n                url: \"https://example.com/api\"\n            }\n        ]\n    },\n    {\n        id: \"logs\",\n        icon: _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n        label: \"日志\",\n        type: \"single\"\n    },\n    {\n        id: \"settings\",\n        icon: _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n        label: \"设置\",\n        type: \"single\"\n    }\n];\n// Enhanced mock data for realistic demonstration\nconst agents = [\n    {\n        id: \"agent1\",\n        name: \"智能客服助手\",\n        url: \"https://example.com/agent1\",\n        type: \"客服\",\n        status: \"在线\",\n        lastActive: \"2分钟前\",\n        tasksCompleted: 127,\n        successRate: 98.5\n    },\n    {\n        id: \"agent2\",\n        name: \"数据分析专家\",\n        url: \"https://example.com/agent2\",\n        type: \"分析\",\n        status: \"忙碌\",\n        lastActive: \"刚刚\",\n        tasksCompleted: 89,\n        successRate: 96.2\n    },\n    {\n        id: \"agent3\",\n        name: \"内容创作助理\",\n        url: \"https://example.com/agent3\",\n        type: \"创作\",\n        status: \"在线\",\n        lastActive: \"5分钟前\",\n        tasksCompleted: 156,\n        successRate: 94.8\n    },\n    {\n        id: \"agent4\",\n        name: \"代码审查机器人\",\n        url: \"https://example.com/agent4\",\n        type: \"开发\",\n        status: \"离线\",\n        lastActive: \"1小时前\",\n        tasksCompleted: 73,\n        successRate: 99.1\n    }\n];\nconst dashboardStats = [\n    {\n        title: \"总代理\",\n        value: \"4\",\n        subtitle: \"3个在线\",\n        color: \"bg-blue-500\",\n        trend: \"+2\",\n        trendDirection: \"up\"\n    },\n    {\n        title: \"CPU使用率\",\n        value: \"42.8%\",\n        subtitle: \"平均负载\",\n        color: \"bg-green-500\",\n        trend: \"-5.2%\",\n        trendDirection: \"down\"\n    },\n    {\n        title: \"内存使用\",\n        value: \"67.3%\",\n        subtitle: \"8.2GB / 12GB\",\n        color: \"bg-purple-500\",\n        trend: \"+3.1%\",\n        trendDirection: \"up\"\n    },\n    {\n        title: \"今日任务\",\n        value: \"445\",\n        subtitle: \"已完成\",\n        color: \"bg-orange-500\",\n        trend: \"+28\",\n        trendDirection: \"up\"\n    }\n];\nconst agentActivity = [\n    {\n        id: 1,\n        agent: \"智能客服助手\",\n        action: \"处理用户咨询\",\n        status: \"已完成\",\n        time: \"刚刚\",\n        color: \"bg-green-500\",\n        duration: \"2分钟\"\n    },\n    {\n        id: 2,\n        agent: \"数据分析专家\",\n        action: \"生成销售报告\",\n        status: \"进行中\",\n        time: \"3分钟前\",\n        color: \"bg-blue-500\",\n        duration: \"预计5分钟\"\n    },\n    {\n        id: 3,\n        agent: \"内容创作助理\",\n        action: \"撰写产品描述\",\n        status: \"已完成\",\n        time: \"5分钟前\",\n        color: \"bg-green-500\",\n        duration: \"8分钟\"\n    },\n    {\n        id: 4,\n        agent: \"智能客服助手\",\n        action: \"更新知识库\",\n        status: \"已完成\",\n        time: \"8分钟前\",\n        color: \"bg-green-500\",\n        duration: \"3分钟\"\n    },\n    {\n        id: 5,\n        agent: \"代码审查机器人\",\n        action: \"代码质量检查\",\n        status: \"等待中\",\n        time: \"12分钟前\",\n        color: \"bg-yellow-500\",\n        duration: \"待处理\"\n    }\n];\n// Performance metrics for charts\nconst performanceData = {\n    systemLoad: [\n        {\n            time: \"00:00\",\n            cpu: 35,\n            memory: 62,\n            network: 45\n        },\n        {\n            time: \"04:00\",\n            cpu: 28,\n            memory: 58,\n            network: 38\n        },\n        {\n            time: \"08:00\",\n            cpu: 42,\n            memory: 65,\n            network: 52\n        },\n        {\n            time: \"12:00\",\n            cpu: 48,\n            memory: 71,\n            network: 61\n        },\n        {\n            time: \"16:00\",\n            cpu: 38,\n            memory: 67,\n            network: 47\n        },\n        {\n            time: \"20:00\",\n            cpu: 33,\n            memory: 63,\n            network: 42\n        }\n    ],\n    agentPerformance: [\n        {\n            name: \"智能客服助手\",\n            completed: 127,\n            success: 98.5,\n            avgTime: 3.2\n        },\n        {\n            name: \"数据分析专家\",\n            completed: 89,\n            success: 96.2,\n            avgTime: 12.5\n        },\n        {\n            name: \"内容创作助理\",\n            completed: 156,\n            success: 94.8,\n            avgTime: 8.7\n        },\n        {\n            name: \"代码审查机器人\",\n            completed: 73,\n            success: 99.1,\n            avgTime: 15.3\n        }\n    ],\n    taskDistribution: [\n        {\n            category: \"客服咨询\",\n            count: 185,\n            percentage: 41.6\n        },\n        {\n            category: \"数据分析\",\n            count: 89,\n            percentage: 20.0\n        },\n        {\n            category: \"内容创作\",\n            count: 156,\n            percentage: 35.1\n        },\n        {\n            category: \"代码审查\",\n            count: 15,\n            percentage: 3.3\n        }\n    ]\n};\nfunction DashboardPage() {\n    _s();\n    const [selectedView, setSelectedView] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"home\") // Changed from selectedAgent to selectedView for home/agent views\n    ;\n    const [username, setUsername] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [sidebarCollapsed, setSidebarCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [mobileMenuOpen, setMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [touchStartX, setTouchStartX] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [touchCurrentX, setTouchCurrentX] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [iframeError, setIframeError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DashboardPage.useEffect\": ()=>{\n            const isLoggedIn = localStorage.getItem(\"isLoggedIn\");\n            const storedUsername = localStorage.getItem(\"username\");\n            if (!isLoggedIn) {\n                router.push(\"/\");\n                return;\n            }\n            if (storedUsername) {\n                setUsername(storedUsername);\n            }\n        }\n    }[\"DashboardPage.useEffect\"], [\n        router\n    ]);\n    // Enhanced mobile menu accessibility and keyboard support\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DashboardPage.useEffect\": ()=>{\n            const handleKeyDown = {\n                \"DashboardPage.useEffect.handleKeyDown\": (e)=>{\n                    if (e.key === 'Escape' && mobileMenuOpen) {\n                        setMobileMenuOpen(false);\n                    }\n                }\n            }[\"DashboardPage.useEffect.handleKeyDown\"];\n            const handleResize = {\n                \"DashboardPage.useEffect.handleResize\": ()=>{\n                    // Close mobile menu when switching to desktop view\n                    if (window.innerWidth >= 1024 && mobileMenuOpen) {\n                        setMobileMenuOpen(false);\n                    }\n                }\n            }[\"DashboardPage.useEffect.handleResize\"];\n            document.addEventListener('keydown', handleKeyDown);\n            window.addEventListener('resize', handleResize);\n            return ({\n                \"DashboardPage.useEffect\": ()=>{\n                    document.removeEventListener('keydown', handleKeyDown);\n                    window.removeEventListener('resize', handleResize);\n                }\n            })[\"DashboardPage.useEffect\"];\n        }\n    }[\"DashboardPage.useEffect\"], [\n        mobileMenuOpen\n    ]);\n    const handleLogout = ()=>{\n        localStorage.removeItem(\"isLoggedIn\");\n        localStorage.removeItem(\"username\");\n        router.push(\"/\");\n    };\n    const toggleSidebar = ()=>{\n        setSidebarCollapsed(!sidebarCollapsed);\n    };\n    const toggleMobileMenu = ()=>{\n        setMobileMenuOpen(!mobileMenuOpen);\n    };\n    // Enhanced touch handling for mobile menu\n    const handleTouchStart = (e)=>{\n        setTouchStartX(e.touches[0].clientX);\n        setTouchCurrentX(e.touches[0].clientX);\n    };\n    const handleTouchMove = (e)=>{\n        if (touchStartX === null) return;\n        setTouchCurrentX(e.touches[0].clientX);\n    };\n    const handleTouchEnd = ()=>{\n        if (touchStartX === null || touchCurrentX === null) return;\n        const deltaX = touchCurrentX - touchStartX;\n        const threshold = 50 // Minimum swipe distance\n        ;\n        // Swipe right to open menu (when closed)\n        if (deltaX > threshold && !mobileMenuOpen) {\n            setMobileMenuOpen(true);\n        } else if (deltaX < -threshold && mobileMenuOpen) {\n            setMobileMenuOpen(false);\n        }\n        setTouchStartX(null);\n        setTouchCurrentX(null);\n    };\n    // Close mobile menu when clicking outside\n    const handleBackdropClick = (e)=>{\n        e.stopPropagation();\n        setMobileMenuOpen(false);\n    };\n    const handleViewSelect = (view)=>{\n        // Renamed from handleAgentSelect to handleViewSelect\n        if (view === \"home\") {\n            setSelectedView(\"home\");\n            setMobileMenuOpen(false);\n            return;\n        }\n        setIsLoading(true);\n        setIframeError(false);\n        setSelectedView(view);\n        setMobileMenuOpen(false);\n        setTimeout(()=>setIsLoading(false), 1000);\n    };\n    const selectedAgentData = agents.find((agent)=>agent.id === selectedView);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-slate-50 flex\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"\\n          fixed inset-0 z-40 lg:hidden transition-all duration-300 ease-in-out\\n          \".concat(mobileMenuOpen ? \"bg-black bg-opacity-50 backdrop-blur-sm visible\" : \"bg-transparent invisible\", \"\\n        \"),\n                onClick: handleBackdropClick,\n                onTouchStart: handleTouchStart,\n                onTouchMove: handleTouchMove,\n                onTouchEnd: handleTouchEnd\n            }, void 0, false, {\n                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 409,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                className: \"\\n          \".concat(mobileMenuOpen ? \"translate-x-0 shadow-2xl\" : \"-translate-x-full shadow-none\", \"\\n          lg:translate-x-0 lg:shadow-none\\n          fixed lg:relative\\n          z-50 lg:z-auto\\n          bg-slate-900\\n          border-r border-slate-700\\n          flex flex-col\\n          transition-all duration-300 ease-out\\n          \").concat(sidebarCollapsed ? \"lg:w-16\" : \"lg:w-80\", \"\\n          w-80\\n          h-screen\\n          lg:transform-none\\n        \"),\n                onTouchStart: handleTouchStart,\n                onTouchMove: handleTouchMove,\n                onTouchEnd: handleTouchEnd,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"border-b border-slate-700/50 \".concat(sidebarCollapsed ? 'p-2' : 'p-3'),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center mb-3 \".concat(sidebarCollapsed ? 'justify-center' : 'justify-between'),\n                                children: [\n                                    !sidebarCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-lg font-semibold text-white\",\n                                        children: \"Agent 管理系统\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 444,\n                                        columnNumber: 35\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"sm\",\n                                                onClick: toggleSidebar,\n                                                className: \"hidden lg:flex text-slate-400 hover:bg-slate-800/80 hover:text-white rounded-lg transition-all duration-200\",\n                                                children: sidebarCollapsed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 452,\n                                                    columnNumber: 37\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 452,\n                                                    columnNumber: 76\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 446,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"sm\",\n                                                onClick: toggleMobileMenu,\n                                                className: \"lg:hidden text-slate-400 hover:bg-slate-800/80 hover:text-white rounded-lg transition-all duration-200 hover:scale-105 active:scale-95\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"transition-transform duration-200 hover:rotate-90\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 461,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 460,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 454,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 445,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 443,\n                                columnNumber: 11\n                            }, this),\n                            sidebarCollapsed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center mb-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-7 h-7 bg-blue-600 rounded-lg flex items-center justify-center cursor-pointer hover:bg-blue-700 transition-all duration-200 hover:scale-105\",\n                                    title: \"\".concat(username || \"手系 Agent\", \" - 管理员\"),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                        className: \"h-3.5 w-3.5 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 474,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 470,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 469,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 p-2.5 bg-slate-800/60 rounded-lg mb-3 border border-slate-700/30\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-7 h-7 bg-blue-600 rounded-lg flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                            className: \"h-3.5 w-3.5 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 480,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 479,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 min-w-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-white truncate\",\n                                                children: username || \"手系 Agent\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 483,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-slate-400\",\n                                                children: \"管理员\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 484,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 482,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 478,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 442,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"flex-1 \".concat(sidebarCollapsed ? 'p-2' : 'p-3', \" overflow-y-auto\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleViewSelect(\"home\"),\n                                        className: \"\\n                  w-full flex items-center rounded-lg text-sm font-medium\\n                  transition-all duration-200 min-h-[36px]\\n                  \".concat(sidebarCollapsed ? 'justify-center px-2 py-2.5' : 'gap-2.5 px-2.5 py-2.5', \"\\n                  \").concat(selectedView === \"home\" ? \"bg-blue-600 text-white shadow-md border border-blue-500/30\" : \"text-slate-300 hover:bg-slate-800/60 hover:text-white\", \"\\n                \"),\n                                        title: sidebarCollapsed ? \"首页\" : undefined,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"h-4 w-4 flex-shrink-0\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 509,\n                                                columnNumber: 17\n                                            }, this),\n                                            !sidebarCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"首页\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 510,\n                                                columnNumber: 39\n                                            }, this),\n                                            !sidebarCollapsed && selectedView === \"home\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"ml-auto w-1.5 h-1.5 bg-white rounded-full\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 512,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 495,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 494,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"pt-3 relative group\",\n                                    children: sidebarCollapsed ? /* Collapsed: Show section icon with hover tooltip */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"\\n                      w-full flex items-center justify-center rounded-lg text-sm font-medium\\n                      transition-all duration-200 min-h-[36px] px-2 py-2.5 cursor-default\\n                      lg:hover:bg-slate-800/60 lg:hover:text-white\\n                      \".concat(agents.some((agent)=>selectedView === agent.id) ? \"bg-blue-600 text-white shadow-md border border-blue-500/30\" : \"text-slate-300\", \"\\n                    \"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                    className: \"h-4 w-4 flex-shrink-0\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 534,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 522,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"hidden lg:block absolute left-full top-0 ml-3 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 ease-out z-50 pointer-events-none group-hover:pointer-events-auto transform translate-x-1 group-hover:translate-x-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-slate-800/95 backdrop-blur-sm border border-slate-700/60 rounded-lg shadow-2xl min-w-[220px] py-2 before:content-[''] before:absolute before:left-[-6px] before:top-4 before:w-0 before:h-0 before:border-t-[6px] before:border-b-[6px] before:border-r-[6px] before:border-transparent before:border-r-slate-800/95\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"px-3 py-2 border-b border-slate-700/50\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-xs font-semibold text-slate-300 uppercase tracking-wider flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                        className: \"h-3 w-3 text-blue-400\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 542,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    \"AI 专家\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 541,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 540,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"py-1\",\n                                                            children: agents.map((agent)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleViewSelect(agent.id),\n                                                                    className: \"\\n                              w-full flex items-center gap-2.5 px-3 py-2.5 text-sm font-medium\\n                              transition-all duration-200 hover:bg-slate-700/80 rounded-md mx-1\\n                              \".concat(selectedView === agent.id ? \"bg-blue-600 text-white shadow-md\" : \"text-slate-300 hover:text-white\", \"\\n                            \"),\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"w-2 h-2 rounded-full flex-shrink-0 \".concat(agent.status === '在线' ? 'bg-green-500' : agent.status === '忙碌' ? 'bg-yellow-500' : 'bg-gray-400')\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                            lineNumber: 561,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"truncate\",\n                                                                            children: agent.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                            lineNumber: 565,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        selectedView === agent.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"ml-auto w-1.5 h-1.5 bg-white rounded-full\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                            lineNumber: 567,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, agent.id, true, {\n                                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 548,\n                                                                    columnNumber: 27\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 546,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 539,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 538,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true) : /* Expanded: Show section header and all agent items */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"px-2.5 py-1.5 mb-1\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-xs font-semibold text-slate-400 uppercase tracking-wider flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            className: \"h-3 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 580,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \"AI 专家\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 579,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 578,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-0.5\",\n                                                children: agents.map((agent)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleViewSelect(agent.id),\n                                                        className: \"\\n                          w-full flex items-center rounded-lg text-sm font-medium\\n                          transition-all duration-200 min-h-[34px] gap-2.5 px-3 py-2 ml-1\\n                          \".concat(selectedView === agent.id ? \"bg-blue-600 text-white shadow-md border border-blue-500/30\" : \"text-slate-300 hover:bg-slate-800/60 hover:text-white\", \"\\n                        \"),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-2 h-2 rounded-full flex-shrink-0 \".concat(agent.status === '在线' ? 'bg-green-500' : agent.status === '忙碌' ? 'bg-yellow-500' : 'bg-gray-400')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 601,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"truncate\",\n                                                                children: agent.name\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 605,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            selectedView === agent.id && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"ml-auto w-1.5 h-1.5 bg-white rounded-full\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 607,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, agent.id, true, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 588,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 586,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 518,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"pt-3 relative group\",\n                                    children: sidebarCollapsed ? /* Collapsed: Show section icon with hover tooltip */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"\\n                      w-full flex items-center justify-center rounded-lg text-sm font-medium\\n                      transition-all duration-200 min-h-[36px] px-2 py-2.5 cursor-default\\n                      lg:hover:bg-slate-800/60 lg:hover:text-white\\n                      \".concat(selectedView === \"analytics\" ? \"bg-blue-600 text-white shadow-md border border-blue-500/30\" : \"text-slate-300\", \"\\n                    \"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                    className: \"h-4 w-4 flex-shrink-0\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 633,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 621,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"hidden lg:block absolute left-full top-0 ml-3 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 ease-out z-50 pointer-events-none group-hover:pointer-events-auto transform translate-x-1 group-hover:translate-x-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-slate-800/95 backdrop-blur-sm border border-slate-700/60 rounded-lg shadow-2xl min-w-[220px] py-2 before:content-[''] before:absolute before:left-[-6px] before:top-4 before:w-0 before:h-0 before:border-t-[6px] before:border-b-[6px] before:border-r-[6px] before:border-transparent before:border-r-slate-800/95\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"px-3 py-2 border-b border-slate-700/50\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-xs font-semibold text-slate-300 uppercase tracking-wider flex items-center gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                        className: \"h-3 w-3 text-purple-400\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 641,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    \"分析工具\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 640,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 639,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"py-1\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>handleViewSelect(\"analytics\"),\n                                                                className: \"\\n                            w-full flex items-center gap-2.5 px-3 py-2.5 text-sm font-medium\\n                            transition-all duration-200 hover:bg-slate-700/80 rounded-md mx-1\\n                            \".concat(selectedView === \"analytics\" ? \"bg-blue-600 text-white shadow-md\" : \"text-slate-300 hover:text-white\", \"\\n                          \"),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                        className: \"h-3.5 w-3.5 flex-shrink-0 text-purple-400\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 658,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"truncate\",\n                                                                        children: \"性能分析\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 659,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    selectedView === \"analytics\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"ml-auto w-1.5 h-1.5 bg-white rounded-full\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 661,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 646,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 645,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 638,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 637,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true) : /* Expanded: Show section header and analytics items */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"px-2.5 py-1.5 mb-1\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-xs font-semibold text-slate-400 uppercase tracking-wider flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                            className: \"h-3 w-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 673,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \"分析工具\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 672,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 671,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-0.5\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>handleViewSelect(\"analytics\"),\n                                                    className: \"\\n                        w-full flex items-center rounded-lg text-sm font-medium\\n                        transition-all duration-200 min-h-[34px] gap-2.5 px-3 py-2 ml-1\\n                        \".concat(selectedView === \"analytics\" ? \"bg-blue-600 text-white shadow-md border border-blue-500/30\" : \"text-slate-300 hover:bg-slate-800/60 hover:text-white\", \"\\n                      \"),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                            className: \"h-3.5 w-3.5 flex-shrink-0\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 692,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"truncate\",\n                                                            children: \"性能分析\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 693,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        selectedView === \"analytics\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"ml-auto w-1.5 h-1.5 bg-white rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 695,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 680,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 679,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 617,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 492,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 490,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"border-t border-slate-700/50 \".concat(sidebarCollapsed ? 'p-2' : 'p-3'),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"ghost\",\n                            onClick: handleLogout,\n                            className: \"\\n              w-full text-slate-300 hover:bg-slate-800/60 hover:text-white rounded-lg\\n              transition-all duration-200 min-h-[36px]\\n              \".concat(sidebarCollapsed ? \"justify-center px-2 py-2\" : \"justify-start px-2.5 py-2\", \"\\n            \"),\n                            title: sidebarCollapsed ? \"退出登录\" : undefined,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 716,\n                                    columnNumber: 13\n                                }, this),\n                                !sidebarCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"ml-2 text-sm\",\n                                    children: \"退出登录\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 717,\n                                    columnNumber: 35\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 706,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 705,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 423,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                variant: \"ghost\",\n                size: \"sm\",\n                onClick: toggleMobileMenu,\n                className: \"\\n          lg:hidden fixed top-3 left-3 z-30\\n          bg-slate-900/95 backdrop-blur-sm text-white\\n          hover:bg-slate-800/90 active:bg-slate-700\\n          border border-slate-700/40 rounded-lg\\n          transition-all duration-200 ease-in-out\\n          hover:scale-105 active:scale-95\\n          shadow-md hover:shadow-lg\\n          min-h-[36px] min-w-[36px]\\n          \".concat(mobileMenuOpen ? 'bg-slate-800/90 scale-105' : '', \"\\n        \"),\n                onTouchStart: handleTouchStart,\n                onTouchMove: handleTouchMove,\n                onTouchEnd: handleTouchEnd,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"transition-transform duration-200 \".concat(mobileMenuOpen ? 'rotate-90' : ''),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 743,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 742,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 723,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"flex-1 p-3 lg:p-4 overflow-auto\",\n                children: selectedView === \"home\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg border border-slate-200 p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-xl font-bold text-slate-900 mb-1\",\n                                    children: [\n                                        \"欢迎回来，\",\n                                        username || \"管理员\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 752,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-slate-600\",\n                                    children: \"AI Agent 管理中心概览\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 753,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 751,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-3\",\n                            children: dashboardStats.map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                    className: \"bg-white border-slate-200 hover:shadow-lg transition-shadow\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                        className: \"p-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs font-medium text-slate-600\",\n                                                                children: stat.title\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 763,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-lg font-bold text-slate-900 mt-1\",\n                                                                children: stat.value\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 764,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-slate-500\",\n                                                                children: stat.subtitle\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 765,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 762,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-10 h-10 \".concat(stat.color, \" rounded-lg flex items-center justify-center\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-5 h-5 bg-white rounded opacity-80\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 768,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 767,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 761,\n                                                columnNumber: 21\n                                            }, this),\n                                            stat.trend && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-2 flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs font-medium \".concat(stat.trendDirection === 'up' ? 'text-green-600' : 'text-red-600'),\n                                                        children: [\n                                                            stat.trendDirection === 'up' ? '↗' : '↘',\n                                                            \" \",\n                                                            stat.trend\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 773,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-slate-500 ml-1\",\n                                                        children: \"vs 昨天\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 778,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 772,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 760,\n                                        columnNumber: 19\n                                    }, this)\n                                }, index, false, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 759,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 757,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                    className: \"bg-white border-slate-200 hover:shadow-lg transition-shadow\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                            className: \"pb-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                className: \"text-slate-900 flex items-center gap-2 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        className: \"h-4 w-4 text-blue-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 792,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Agent 性能\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 791,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 790,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                            className: \"space-y-2\",\n                                            children: performanceData.agentPerformance.slice(0, 3).map((agent, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between p-2 bg-slate-50 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1 min-w-0\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs font-medium text-slate-900 truncate\",\n                                                                    children: agent.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 800,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-slate-500\",\n                                                                    children: [\n                                                                        \"成功率: \",\n                                                                        agent.success,\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 801,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 799,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-right\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm font-bold text-blue-600\",\n                                                                    children: agent.completed\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 804,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-slate-500\",\n                                                                    children: \"任务\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 805,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 803,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 798,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 796,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 789,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                    className: \"bg-white border-slate-200 hover:shadow-lg transition-shadow\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                            className: \"pb-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                className: \"text-slate-900 flex items-center gap-2 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                        className: \"h-4 w-4 text-purple-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 816,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"系统负载\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 815,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 814,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                            className: \"space-y-2\",\n                                            children: performanceData.systemLoad.slice(-3).map((load, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between text-xs\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-slate-600\",\n                                                                    children: load.time\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 824,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-slate-900 font-medium\",\n                                                                    children: [\n                                                                        \"CPU: \",\n                                                                        load.cpu,\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 825,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 823,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-full bg-slate-200 rounded-full h-1.5\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-purple-600 h-1.5 rounded-full transition-all duration-300\",\n                                                                style: {\n                                                                    width: \"\".concat(load.cpu, \"%\")\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 828,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 827,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 822,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 820,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 813,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                    className: \"bg-white border-slate-200 md:col-span-2 xl:col-span-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                            className: \"pb-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                className: \"text-slate-900 flex items-center gap-2 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                        className: \"h-4 w-4 text-orange-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 842,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"最近活动\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 841,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 840,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                            className: \"space-y-2\",\n                                            children: agentActivity.slice(0, 3).map((activity)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2 p-2 bg-slate-50 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 rounded-full \".concat(activity.color)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 849,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1 min-w-0\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs font-medium text-slate-900 truncate\",\n                                                                    children: activity.agent\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 851,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-slate-600 truncate\",\n                                                                    children: activity.action\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 852,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 850,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-right\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs font-medium \".concat(activity.status === '已完成' ? 'text-green-600' : activity.status === '进行中' ? 'text-blue-600' : 'text-yellow-600'),\n                                                                children: activity.status\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 855,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 854,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, activity.id, true, {\n                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 848,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 846,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 839,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 787,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            className: \"bg-white border-slate-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                    className: \"pb-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                        className: \"text-slate-900 flex items-center gap-2 text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                className: \"h-4 w-4 text-indigo-600\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 872,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Agent 状态总览\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 871,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 870,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-2\",\n                                        children: agents.map((agent)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between p-2 bg-slate-50 rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-2 h-2 rounded-full \".concat(agent.status === '在线' ? 'bg-green-500' : agent.status === '忙碌' ? 'bg-yellow-500' : 'bg-gray-400')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 881,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"min-w-0\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs font-medium text-slate-900 truncate\",\n                                                                        children: agent.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 886,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-slate-500\",\n                                                                        children: agent.type\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 887,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 885,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 880,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-right\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs font-medium text-slate-900\",\n                                                            children: agent.tasksCompleted\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 891,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 890,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, agent.id, true, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 879,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 877,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 876,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 869,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 749,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                    className: \"h-full bg-white border-slate-200\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        className: \"h-full p-0\",\n                        children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-full flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                        className: \"h-8 w-8 animate-spin text-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 905,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-slate-600\",\n                                        children: [\n                                            \"加载 \",\n                                            selectedAgentData === null || selectedAgentData === void 0 ? void 0 : selectedAgentData.name,\n                                            \"...\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 906,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 904,\n                                columnNumber: 19\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 903,\n                            columnNumber: 17\n                        }, this) : iframeError ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-full flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center gap-4 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                        className: \"h-12 w-12 text-red-500\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 912,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-slate-900 font-medium\",\n                                                children: \"加载失败\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 914,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-slate-600 text-sm mt-1\",\n                                                children: [\n                                                    \"无法加载 \",\n                                                    selectedAgentData === null || selectedAgentData === void 0 ? void 0 : selectedAgentData.name\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 915,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 913,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        onClick: ()=>handleViewSelect(selectedView),\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        children: \"重试\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 917,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 911,\n                                columnNumber: 19\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 910,\n                            columnNumber: 17\n                        }, this) : selectedAgentData ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"iframe\", {\n                            src: selectedAgentData.url,\n                            className: \"w-full h-full border-0 rounded-lg\",\n                            title: selectedAgentData.name,\n                            onError: ()=>setIframeError(true),\n                            sandbox: \"allow-scripts allow-same-origin allow-forms\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 923,\n                            columnNumber: 17\n                        }, this) : null\n                    }, void 0, false, {\n                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 901,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 900,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 747,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 407,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardPage, \"LZ5QUMNVC8kN/tQl1hjuN5TcKSw=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = DashboardPage;\nvar _c;\n$RefreshReg$(_c, \"DashboardPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/dashboard/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/calendar.js":
/*!*******************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/calendar.js ***!
  \*******************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Calendar)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.454.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst Calendar = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Calendar\", [\n    [\n        \"path\",\n        {\n            d: \"M8 2v4\",\n            key: \"1cmpym\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M16 2v4\",\n            key: \"4m81vk\"\n        }\n    ],\n    [\n        \"rect\",\n        {\n            width: \"18\",\n            height: \"18\",\n            x: \"3\",\n            y: \"4\",\n            rx: \"2\",\n            key: \"1hopcy\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M3 10h18\",\n            key: \"8toen8\"\n        }\n    ]\n]);\n //# sourceMappingURL=calendar.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/calendar.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/container.js":
/*!********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/container.js ***!
  \********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Container)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.454.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst Container = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Container\", [\n    [\n        \"path\",\n        {\n            d: \"M22 7.7c0-.6-.4-1.2-.8-1.5l-6.3-3.9a1.72 1.72 0 0 0-1.7 0l-10.3 6c-.5.2-.9.8-.9 1.4v6.6c0 .5.4 1.2.8 1.5l6.3 3.9a1.72 1.72 0 0 0 1.7 0l10.3-6c.5-.3.9-1 .9-1.5Z\",\n            key: \"1t2lqe\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M10 21.9V14L2.1 9.1\",\n            key: \"o7czzq\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m10 14 11.9-6.9\",\n            key: \"zm5e20\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M14 19.8v-8.1\",\n            key: \"159ecu\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M18 17.5V9.4\",\n            key: \"11uown\"\n        }\n    ]\n]);\n //# sourceMappingURL=container.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/container.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/database.js":
/*!*******************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/database.js ***!
  \*******************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Database)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.454.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst Database = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Database\", [\n    [\n        \"ellipse\",\n        {\n            cx: \"12\",\n            cy: \"5\",\n            rx: \"9\",\n            ry: \"3\",\n            key: \"msslwz\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M3 5V19A9 3 0 0 0 21 19V5\",\n            key: \"1wlel7\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M3 12A9 3 0 0 0 21 12\",\n            key: \"mv7ke4\"\n        }\n    ]\n]);\n //# sourceMappingURL=database.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/database.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/file-text.js":
/*!********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/file-text.js ***!
  \********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ FileText)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.454.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst FileText = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"FileText\", [\n    [\n        \"path\",\n        {\n            d: \"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z\",\n            key: \"1rqfz7\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M14 2v4a2 2 0 0 0 2 2h4\",\n            key: \"tnqrlb\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M10 9H8\",\n            key: \"b1mrlr\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M16 13H8\",\n            key: \"t4e002\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M16 17H8\",\n            key: \"z1uh3a\"\n        }\n    ]\n]);\n //# sourceMappingURL=file-text.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/file-text.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/globe.js":
/*!****************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/globe.js ***!
  \****************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Globe)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.454.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst Globe = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Globe\", [\n    [\n        \"circle\",\n        {\n            cx: \"12\",\n            cy: \"12\",\n            r: \"10\",\n            key: \"1mglay\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20\",\n            key: \"13o1zl\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M2 12h20\",\n            key: \"9i4pu4\"\n        }\n    ]\n]);\n //# sourceMappingURL=globe.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/globe.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/package.js":
/*!******************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/package.js ***!
  \******************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Package)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.454.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst Package = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Package\", [\n    [\n        \"path\",\n        {\n            d: \"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z\",\n            key: \"1a0edw\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M12 22V12\",\n            key: \"d0xqtd\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m3.3 7 7.703 4.734a2 2 0 0 0 1.994 0L20.7 7\",\n            key: \"yx3hmr\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"m7.5 4.27 9 5.15\",\n            key: \"1c824w\"\n        }\n    ]\n]);\n //# sourceMappingURL=package.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9sdWNpZGUtcmVhY3RAMC40NTQuMF9yZWFjdEAxOS4xLjEvbm9kZV9tb2R1bGVzL2x1Y2lkZS1yZWFjdC9kaXN0L2VzbS9pY29ucy9wYWNrYWdlLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBYU0sZ0JBQVUsZ0VBQWdCLENBQUMsU0FBVztJQUMxQztRQUNFO1FBQ0E7WUFDRSxDQUFHO1lBQ0gsR0FBSztRQUNQO0tBQ0Y7SUFDQTtRQUFDLE1BQVE7UUFBQTtZQUFFLEdBQUcsQ0FBYTtZQUFBLEtBQUs7UUFBQSxDQUFVO0tBQUE7SUFDMUM7UUFBQyxNQUFRO1FBQUE7WUFBRSxHQUFHLENBQStDO1lBQUEsS0FBSztRQUFBLENBQVU7S0FBQTtJQUM1RTtRQUFDLE1BQVE7UUFBQTtZQUFFLEdBQUcsQ0FBb0I7WUFBQSxLQUFLO1FBQUEsQ0FBVTtLQUFBO0NBQ2xEIiwic291cmNlcyI6WyJEOlxcbXlfcHJvamVjdFxcc3JjXFxpY29uc1xccGFja2FnZS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgY3JlYXRlTHVjaWRlSWNvbiBmcm9tICcuLi9jcmVhdGVMdWNpZGVJY29uJztcblxuLyoqXG4gKiBAY29tcG9uZW50IEBuYW1lIFBhY2thZ2VcbiAqIEBkZXNjcmlwdGlvbiBMdWNpZGUgU1ZHIGljb24gY29tcG9uZW50LCByZW5kZXJzIFNWRyBFbGVtZW50IHdpdGggY2hpbGRyZW4uXG4gKlxuICogQHByZXZpZXcgIVtpbWddKGRhdGE6aW1hZ2Uvc3ZnK3htbDtiYXNlNjQsUEhOMlp5QWdlRzFzYm5NOUltaDBkSEE2THk5M2QzY3Vkek11YjNKbkx6SXdNREF2YzNabklnb2dJSGRwWkhSb1BTSXlOQ0lLSUNCb1pXbG5hSFE5SWpJMElnb2dJSFpwWlhkQ2IzZzlJakFnTUNBeU5DQXlOQ0lLSUNCbWFXeHNQU0p1YjI1bElnb2dJSE4wY205clpUMGlJekF3TUNJZ2MzUjViR1U5SW1KaFkydG5jbTkxYm1RdFkyOXNiM0k2SUNObVptWTdJR0p2Y21SbGNpMXlZV1JwZFhNNklESndlQ0lLSUNCemRISnZhMlV0ZDJsa2RHZzlJaklpQ2lBZ2MzUnliMnRsTFd4cGJtVmpZWEE5SW5KdmRXNWtJZ29nSUhOMGNtOXJaUzFzYVc1bGFtOXBiajBpY205MWJtUWlDajRLSUNBOGNHRjBhQ0JrUFNKTk1URWdNakV1TnpOaE1pQXlJREFnTUNBd0lESWdNR3czTFRSQk1pQXlJREFnTUNBd0lESXhJREUyVmpoaE1pQXlJREFnTUNBd0xURXRNUzQzTTJ3dE55MDBZVElnTWlBd0lEQWdNQzB5SURCc0xUY2dORUV5SURJZ01DQXdJREFnTXlBNGRqaGhNaUF5SURBZ01DQXdJREVnTVM0M00zb2lJQzgrQ2lBZ1BIQmhkR2dnWkQwaVRURXlJREl5VmpFeUlpQXZQZ29nSUR4d1lYUm9JR1E5SW0wekxqTWdOeUEzTGpjd015QTBMamN6TkdFeUlESWdNQ0F3SURBZ01TNDVPVFFnTUV3eU1DNDNJRGNpSUM4K0NpQWdQSEJoZEdnZ1pEMGliVGN1TlNBMExqSTNJRGtnTlM0eE5TSWdMejRLUEM5emRtYytDZz09KSAtIGh0dHBzOi8vbHVjaWRlLmRldi9pY29ucy9wYWNrYWdlXG4gKiBAc2VlIGh0dHBzOi8vbHVjaWRlLmRldi9ndWlkZS9wYWNrYWdlcy9sdWNpZGUtcmVhY3QgLSBEb2N1bWVudGF0aW9uXG4gKlxuICogQHBhcmFtIHtPYmplY3R9IHByb3BzIC0gTHVjaWRlIGljb25zIHByb3BzIGFuZCBhbnkgdmFsaWQgU1ZHIGF0dHJpYnV0ZVxuICogQHJldHVybnMge0pTWC5FbGVtZW50fSBKU1ggRWxlbWVudFxuICpcbiAqL1xuY29uc3QgUGFja2FnZSA9IGNyZWF0ZUx1Y2lkZUljb24oJ1BhY2thZ2UnLCBbXG4gIFtcbiAgICAncGF0aCcsXG4gICAge1xuICAgICAgZDogJ00xMSAyMS43M2EyIDIgMCAwIDAgMiAwbDctNEEyIDIgMCAwIDAgMjEgMTZWOGEyIDIgMCAwIDAtMS0xLjczbC03LTRhMiAyIDAgMCAwLTIgMGwtNyA0QTIgMiAwIDAgMCAzIDh2OGEyIDIgMCAwIDAgMSAxLjczeicsXG4gICAgICBrZXk6ICcxYTBlZHcnLFxuICAgIH0sXG4gIF0sXG4gIFsncGF0aCcsIHsgZDogJ00xMiAyMlYxMicsIGtleTogJ2QweHF0ZCcgfV0sXG4gIFsncGF0aCcsIHsgZDogJ20zLjMgNyA3LjcwMyA0LjczNGEyIDIgMCAwIDAgMS45OTQgMEwyMC43IDcnLCBrZXk6ICd5eDNobXInIH1dLFxuICBbJ3BhdGgnLCB7IGQ6ICdtNy41IDQuMjcgOSA1LjE1Jywga2V5OiAnMWM4MjR3JyB9XSxcbl0pO1xuXG5leHBvcnQgZGVmYXVsdCBQYWNrYWdlO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/package.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/server.js":
/*!*****************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/server.js ***!
  \*****************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Server)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.454.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst Server = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Server\", [\n    [\n        \"rect\",\n        {\n            width: \"20\",\n            height: \"8\",\n            x: \"2\",\n            y: \"2\",\n            rx: \"2\",\n            ry: \"2\",\n            key: \"ngkwjq\"\n        }\n    ],\n    [\n        \"rect\",\n        {\n            width: \"20\",\n            height: \"8\",\n            x: \"2\",\n            y: \"14\",\n            rx: \"2\",\n            ry: \"2\",\n            key: \"iecqi9\"\n        }\n    ],\n    [\n        \"line\",\n        {\n            x1: \"6\",\n            x2: \"6.01\",\n            y1: \"6\",\n            y2: \"6\",\n            key: \"16zg32\"\n        }\n    ],\n    [\n        \"line\",\n        {\n            x1: \"6\",\n            x2: \"6.01\",\n            y1: \"18\",\n            y2: \"18\",\n            key: \"nzw8ys\"\n        }\n    ]\n]);\n //# sourceMappingURL=server.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/server.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/settings.js":
/*!*******************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/settings.js ***!
  \*******************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Settings)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.454.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst Settings = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Settings\", [\n    [\n        \"path\",\n        {\n            d: \"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z\",\n            key: \"1qme2f\"\n        }\n    ],\n    [\n        \"circle\",\n        {\n            cx: \"12\",\n            cy: \"12\",\n            r: \"3\",\n            key: \"1v7zrd\"\n        }\n    ]\n]);\n //# sourceMappingURL=settings.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/settings.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/terminal.js":
/*!*******************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/terminal.js ***!
  \*******************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Terminal)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.454.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst Terminal = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Terminal\", [\n    [\n        \"polyline\",\n        {\n            points: \"4 17 10 11 4 5\",\n            key: \"akl6gq\"\n        }\n    ],\n    [\n        \"line\",\n        {\n            x1: \"12\",\n            x2: \"20\",\n            y1: \"19\",\n            y2: \"19\",\n            key: \"q2wloq\"\n        }\n    ]\n]);\n //# sourceMappingURL=terminal.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/terminal.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/wrench.js":
/*!*****************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/wrench.js ***!
  \*****************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Wrench)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.454.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst Wrench = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Wrench\", [\n    [\n        \"path\",\n        {\n            d: \"M14.7 6.3a1 1 0 0 0 0 1.4l1.6 1.6a1 1 0 0 0 1.4 0l3.77-3.77a6 6 0 0 1-7.94 7.94l-6.91 6.91a2.12 2.12 0 0 1-3-3l6.91-6.91a6 6 0 0 1 7.94-7.94l-3.76 3.76z\",\n            key: \"cbrjhi\"\n        }\n    ]\n]);\n //# sourceMappingURL=wrench.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/wrench.js\n"));

/***/ })

});