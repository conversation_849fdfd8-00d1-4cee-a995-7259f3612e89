"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./app/dashboard/page.tsx":
/*!********************************!*\
  !*** ./app/dashboard/page.tsx ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,Calendar,ChevronLeft,ChevronRight,Container,Database,FileText,Globe,Home,LineChart,Loader2,LogOut,Menu,Package,Server,Settings,Terminal,User,Users,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,Calendar,ChevronLeft,ChevronRight,Container,Database,FileText,Globe,Home,LineChart,Loader2,LogOut,Menu,Package,Server,Settings,Terminal,User,Users,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,Calendar,ChevronLeft,ChevronRight,Container,Database,FileText,Globe,Home,LineChart,Loader2,LogOut,Menu,Package,Server,Settings,Terminal,User,Users,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,Calendar,ChevronLeft,ChevronRight,Container,Database,FileText,Globe,Home,LineChart,Loader2,LogOut,Menu,Package,Server,Settings,Terminal,User,Users,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,Calendar,ChevronLeft,ChevronRight,Container,Database,FileText,Globe,Home,LineChart,Loader2,LogOut,Menu,Package,Server,Settings,Terminal,User,Users,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,Calendar,ChevronLeft,ChevronRight,Container,Database,FileText,Globe,Home,LineChart,Loader2,LogOut,Menu,Package,Server,Settings,Terminal,User,Users,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/container.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,Calendar,ChevronLeft,ChevronRight,Container,Database,FileText,Globe,Home,LineChart,Loader2,LogOut,Menu,Package,Server,Settings,Terminal,User,Users,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/server.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,Calendar,ChevronLeft,ChevronRight,Container,Database,FileText,Globe,Home,LineChart,Loader2,LogOut,Menu,Package,Server,Settings,Terminal,User,Users,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/terminal.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,Calendar,ChevronLeft,ChevronRight,Container,Database,FileText,Globe,Home,LineChart,Loader2,LogOut,Menu,Package,Server,Settings,Terminal,User,Users,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,Calendar,ChevronLeft,ChevronRight,Container,Database,FileText,Globe,Home,LineChart,Loader2,LogOut,Menu,Package,Server,Settings,Terminal,User,Users,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/wrench.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,Calendar,ChevronLeft,ChevronRight,Container,Database,FileText,Globe,Home,LineChart,Loader2,LogOut,Menu,Package,Server,Settings,Terminal,User,Users,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,Calendar,ChevronLeft,ChevronRight,Container,Database,FileText,Globe,Home,LineChart,Loader2,LogOut,Menu,Package,Server,Settings,Terminal,User,Users,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,Calendar,ChevronLeft,ChevronRight,Container,Database,FileText,Globe,Home,LineChart,Loader2,LogOut,Menu,Package,Server,Settings,Terminal,User,Users,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,Calendar,ChevronLeft,ChevronRight,Container,Database,FileText,Globe,Home,LineChart,Loader2,LogOut,Menu,Package,Server,Settings,Terminal,User,Users,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,Calendar,ChevronLeft,ChevronRight,Container,Database,FileText,Globe,Home,LineChart,Loader2,LogOut,Menu,Package,Server,Settings,Terminal,User,Users,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,Calendar,ChevronLeft,ChevronRight,Container,Database,FileText,Globe,Home,LineChart,Loader2,LogOut,Menu,Package,Server,Settings,Terminal,User,Users,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,Calendar,ChevronLeft,ChevronRight,Container,Database,FileText,Globe,Home,LineChart,Loader2,LogOut,Menu,Package,Server,Settings,Terminal,User,Users,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,Calendar,ChevronLeft,ChevronRight,Container,Database,FileText,Globe,Home,LineChart,Loader2,LogOut,Menu,Package,Server,Settings,Terminal,User,Users,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,Calendar,ChevronLeft,ChevronRight,Container,Database,FileText,Globe,Home,LineChart,Loader2,LogOut,Menu,Package,Server,Settings,Terminal,User,Users,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,Calendar,ChevronLeft,ChevronRight,Container,Database,FileText,Globe,Home,LineChart,Loader2,LogOut,Menu,Package,Server,Settings,Terminal,User,Users,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/chart-line.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,Calendar,ChevronLeft,ChevronRight,Container,Database,FileText,Globe,Home,LineChart,Loader2,LogOut,Menu,Package,Server,Settings,Terminal,User,Users,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,Calendar,ChevronLeft,ChevronRight,Container,Database,FileText,Globe,Home,LineChart,Loader2,LogOut,Menu,Package,Server,Settings,Terminal,User,Users,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,Calendar,ChevronLeft,ChevronRight,Container,Database,FileText,Globe,Home,LineChart,Loader2,LogOut,Menu,Package,Server,Settings,Terminal,User,Users,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,Calendar,ChevronLeft,ChevronRight,Container,Database,FileText,Globe,Home,LineChart,Loader2,LogOut,Menu,Package,Server,Settings,Terminal,User,Users,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n// Menu configuration matching the reference design\nconst menuItems = [\n    {\n        id: \"home\",\n        icon: _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        label: \"首页\",\n        type: \"single\"\n    },\n    {\n        id: \"apps\",\n        icon: _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        label: \"应用\",\n        type: \"single\",\n        isActive: true // This represents the current active menu item\n    },\n    {\n        id: \"website\",\n        icon: _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        label: \"网站\",\n        type: \"group\",\n        children: [\n            {\n                id: \"website-1\",\n                label: \"网站管理\",\n                url: \"https://example.com/website1\"\n            },\n            {\n                id: \"website-2\",\n                label: \"域名管理\",\n                url: \"https://example.com/website2\"\n            },\n            {\n                id: \"website-3\",\n                label: \"SSL证书\",\n                url: \"https://example.com/website3\"\n            }\n        ]\n    },\n    {\n        id: \"ai\",\n        icon: _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        label: \"AI\",\n        type: \"group\",\n        children: [\n            {\n                id: \"ai-chat\",\n                label: \"智能对话\",\n                url: \"https://example.com/ai-chat\"\n            },\n            {\n                id: \"ai-analysis\",\n                label: \"数据分析\",\n                url: \"https://example.com/ai-analysis\"\n            },\n            {\n                id: \"ai-content\",\n                label: \"内容生成\",\n                url: \"https://example.com/ai-content\"\n            }\n        ]\n    },\n    {\n        id: \"database\",\n        icon: _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        label: \"数据库\",\n        type: \"single\"\n    },\n    {\n        id: \"docker\",\n        icon: _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        label: \"容器\",\n        type: \"single\"\n    },\n    {\n        id: \"system\",\n        icon: _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n        label: \"系统\",\n        type: \"group\",\n        children: [\n            {\n                id: \"system-monitor\",\n                label: \"系统监控\",\n                url: \"https://example.com/monitor\"\n            },\n            {\n                id: \"system-logs\",\n                label: \"系统日志\",\n                url: \"https://example.com/logs\"\n            },\n            {\n                id: \"system-users\",\n                label: \"用户管理\",\n                url: \"https://example.com/users\"\n            }\n        ]\n    },\n    {\n        id: \"terminal\",\n        icon: _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n        label: \"终端\",\n        type: \"single\"\n    },\n    {\n        id: \"schedule\",\n        icon: _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n        label: \"计划\",\n        type: \"single\"\n    },\n    {\n        id: \"tools\",\n        icon: _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n        label: \"工具\",\n        type: \"single\"\n    },\n    {\n        id: \"advanced\",\n        icon: _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        label: \"高级功能\",\n        type: \"group\",\n        children: [\n            {\n                id: \"advanced-backup\",\n                label: \"备份管理\",\n                url: \"https://example.com/backup\"\n            },\n            {\n                id: \"advanced-security\",\n                label: \"安全设置\",\n                url: \"https://example.com/security\"\n            },\n            {\n                id: \"advanced-api\",\n                label: \"API管理\",\n                url: \"https://example.com/api\"\n            }\n        ]\n    },\n    {\n        id: \"logs\",\n        icon: _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n        label: \"日志\",\n        type: \"single\"\n    },\n    {\n        id: \"settings\",\n        icon: _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n        label: \"设置\",\n        type: \"single\"\n    }\n];\n// Enhanced mock data for realistic demonstration\nconst agents = [\n    {\n        id: \"agent1\",\n        name: \"智能客服助手\",\n        url: \"https://example.com/agent1\",\n        type: \"客服\",\n        status: \"在线\",\n        lastActive: \"2分钟前\",\n        tasksCompleted: 127,\n        successRate: 98.5\n    },\n    {\n        id: \"agent2\",\n        name: \"数据分析专家\",\n        url: \"https://example.com/agent2\",\n        type: \"分析\",\n        status: \"忙碌\",\n        lastActive: \"刚刚\",\n        tasksCompleted: 89,\n        successRate: 96.2\n    },\n    {\n        id: \"agent3\",\n        name: \"内容创作助理\",\n        url: \"https://example.com/agent3\",\n        type: \"创作\",\n        status: \"在线\",\n        lastActive: \"5分钟前\",\n        tasksCompleted: 156,\n        successRate: 94.8\n    },\n    {\n        id: \"agent4\",\n        name: \"代码审查机器人\",\n        url: \"https://example.com/agent4\",\n        type: \"开发\",\n        status: \"离线\",\n        lastActive: \"1小时前\",\n        tasksCompleted: 73,\n        successRate: 99.1\n    }\n];\nconst dashboardStats = [\n    {\n        title: \"总代理\",\n        value: \"4\",\n        subtitle: \"3个在线\",\n        color: \"bg-blue-500\",\n        trend: \"+2\",\n        trendDirection: \"up\"\n    },\n    {\n        title: \"CPU使用率\",\n        value: \"42.8%\",\n        subtitle: \"平均负载\",\n        color: \"bg-green-500\",\n        trend: \"-5.2%\",\n        trendDirection: \"down\"\n    },\n    {\n        title: \"内存使用\",\n        value: \"67.3%\",\n        subtitle: \"8.2GB / 12GB\",\n        color: \"bg-purple-500\",\n        trend: \"+3.1%\",\n        trendDirection: \"up\"\n    },\n    {\n        title: \"今日任务\",\n        value: \"445\",\n        subtitle: \"已完成\",\n        color: \"bg-orange-500\",\n        trend: \"+28\",\n        trendDirection: \"up\"\n    }\n];\nconst agentActivity = [\n    {\n        id: 1,\n        agent: \"智能客服助手\",\n        action: \"处理用户咨询\",\n        status: \"已完成\",\n        time: \"刚刚\",\n        color: \"bg-green-500\",\n        duration: \"2分钟\"\n    },\n    {\n        id: 2,\n        agent: \"数据分析专家\",\n        action: \"生成销售报告\",\n        status: \"进行中\",\n        time: \"3分钟前\",\n        color: \"bg-blue-500\",\n        duration: \"预计5分钟\"\n    },\n    {\n        id: 3,\n        agent: \"内容创作助理\",\n        action: \"撰写产品描述\",\n        status: \"已完成\",\n        time: \"5分钟前\",\n        color: \"bg-green-500\",\n        duration: \"8分钟\"\n    },\n    {\n        id: 4,\n        agent: \"智能客服助手\",\n        action: \"更新知识库\",\n        status: \"已完成\",\n        time: \"8分钟前\",\n        color: \"bg-green-500\",\n        duration: \"3分钟\"\n    },\n    {\n        id: 5,\n        agent: \"代码审查机器人\",\n        action: \"代码质量检查\",\n        status: \"等待中\",\n        time: \"12分钟前\",\n        color: \"bg-yellow-500\",\n        duration: \"待处理\"\n    }\n];\n// Performance metrics for charts\nconst performanceData = {\n    systemLoad: [\n        {\n            time: \"00:00\",\n            cpu: 35,\n            memory: 62,\n            network: 45\n        },\n        {\n            time: \"04:00\",\n            cpu: 28,\n            memory: 58,\n            network: 38\n        },\n        {\n            time: \"08:00\",\n            cpu: 42,\n            memory: 65,\n            network: 52\n        },\n        {\n            time: \"12:00\",\n            cpu: 48,\n            memory: 71,\n            network: 61\n        },\n        {\n            time: \"16:00\",\n            cpu: 38,\n            memory: 67,\n            network: 47\n        },\n        {\n            time: \"20:00\",\n            cpu: 33,\n            memory: 63,\n            network: 42\n        }\n    ],\n    agentPerformance: [\n        {\n            name: \"智能客服助手\",\n            completed: 127,\n            success: 98.5,\n            avgTime: 3.2\n        },\n        {\n            name: \"数据分析专家\",\n            completed: 89,\n            success: 96.2,\n            avgTime: 12.5\n        },\n        {\n            name: \"内容创作助理\",\n            completed: 156,\n            success: 94.8,\n            avgTime: 8.7\n        },\n        {\n            name: \"代码审查机器人\",\n            completed: 73,\n            success: 99.1,\n            avgTime: 15.3\n        }\n    ],\n    taskDistribution: [\n        {\n            category: \"客服咨询\",\n            count: 185,\n            percentage: 41.6\n        },\n        {\n            category: \"数据分析\",\n            count: 89,\n            percentage: 20.0\n        },\n        {\n            category: \"内容创作\",\n            count: 156,\n            percentage: 35.1\n        },\n        {\n            category: \"代码审查\",\n            count: 15,\n            percentage: 3.3\n        }\n    ]\n};\nfunction DashboardPage() {\n    var _menuItems_find;\n    _s();\n    const [selectedView, setSelectedView] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"apps\") // Default to apps view to match reference\n    ;\n    const [username, setUsername] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [sidebarCollapsed, setSidebarCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true) // Default to collapsed like reference\n    ;\n    const [mobileMenuOpen, setMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [touchStartX, setTouchStartX] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [touchCurrentX, setTouchCurrentX] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [iframeError, setIframeError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [expandedGroups, setExpandedGroups] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set()) // Track expanded menu groups\n    ;\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DashboardPage.useEffect\": ()=>{\n            const isLoggedIn = localStorage.getItem(\"isLoggedIn\");\n            const storedUsername = localStorage.getItem(\"username\");\n            if (!isLoggedIn) {\n                router.push(\"/\");\n                return;\n            }\n            if (storedUsername) {\n                setUsername(storedUsername);\n            }\n        }\n    }[\"DashboardPage.useEffect\"], [\n        router\n    ]);\n    // Enhanced mobile menu accessibility and keyboard support\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DashboardPage.useEffect\": ()=>{\n            const handleKeyDown = {\n                \"DashboardPage.useEffect.handleKeyDown\": (e)=>{\n                    if (e.key === 'Escape' && mobileMenuOpen) {\n                        setMobileMenuOpen(false);\n                    }\n                }\n            }[\"DashboardPage.useEffect.handleKeyDown\"];\n            const handleResize = {\n                \"DashboardPage.useEffect.handleResize\": ()=>{\n                    // Close mobile menu when switching to desktop view\n                    if (window.innerWidth >= 1024 && mobileMenuOpen) {\n                        setMobileMenuOpen(false);\n                    }\n                }\n            }[\"DashboardPage.useEffect.handleResize\"];\n            document.addEventListener('keydown', handleKeyDown);\n            window.addEventListener('resize', handleResize);\n            return ({\n                \"DashboardPage.useEffect\": ()=>{\n                    document.removeEventListener('keydown', handleKeyDown);\n                    window.removeEventListener('resize', handleResize);\n                }\n            })[\"DashboardPage.useEffect\"];\n        }\n    }[\"DashboardPage.useEffect\"], [\n        mobileMenuOpen\n    ]);\n    const handleLogout = ()=>{\n        localStorage.removeItem(\"isLoggedIn\");\n        localStorage.removeItem(\"username\");\n        router.push(\"/\");\n    };\n    const toggleSidebar = ()=>{\n        setSidebarCollapsed(!sidebarCollapsed);\n    };\n    const toggleMobileMenu = ()=>{\n        setMobileMenuOpen(!mobileMenuOpen);\n    };\n    // Enhanced touch handling for mobile menu\n    const handleTouchStart = (e)=>{\n        setTouchStartX(e.touches[0].clientX);\n        setTouchCurrentX(e.touches[0].clientX);\n    };\n    const handleTouchMove = (e)=>{\n        if (touchStartX === null) return;\n        setTouchCurrentX(e.touches[0].clientX);\n    };\n    const handleTouchEnd = ()=>{\n        if (touchStartX === null || touchCurrentX === null) return;\n        const deltaX = touchCurrentX - touchStartX;\n        const threshold = 50 // Minimum swipe distance\n        ;\n        // Swipe right to open menu (when closed)\n        if (deltaX > threshold && !mobileMenuOpen) {\n            setMobileMenuOpen(true);\n        } else if (deltaX < -threshold && mobileMenuOpen) {\n            setMobileMenuOpen(false);\n        }\n        setTouchStartX(null);\n        setTouchCurrentX(null);\n    };\n    // Close mobile menu when clicking outside\n    const handleBackdropClick = (e)=>{\n        e.stopPropagation();\n        setMobileMenuOpen(false);\n    };\n    const handleViewSelect = (view)=>{\n        // Handle different view types\n        if (view === \"home\" || view === \"apps\") {\n            setSelectedView(view);\n            setMobileMenuOpen(false);\n            return;\n        }\n        // For other views, simulate loading\n        setIsLoading(true);\n        setIframeError(false);\n        setSelectedView(view);\n        setMobileMenuOpen(false);\n        setTimeout(()=>setIsLoading(false), 1000);\n    };\n    const handleMenuItemClick = (item)=>{\n        if (item.type === \"single\") {\n            handleViewSelect(item.id);\n        } else if (item.type === \"group\") {\n            // Toggle group expansion when sidebar is expanded\n            if (!sidebarCollapsed) {\n                setExpandedGroups((prev)=>{\n                    const newSet = new Set(prev);\n                    if (newSet.has(item.id)) {\n                        newSet.delete(item.id);\n                    } else {\n                        newSet.add(item.id);\n                    }\n                    return newSet;\n                });\n            }\n        }\n    };\n    const handleSubItemClick = (subItem)=>{\n        setIsLoading(true);\n        setIframeError(false);\n        setSelectedView(subItem.id);\n        setMobileMenuOpen(false);\n        setTimeout(()=>setIsLoading(false), 1000);\n    };\n    const selectedAgentData = agents.find((agent)=>agent.id === selectedView);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-slate-50 flex\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"\\n          fixed inset-0 z-40 lg:hidden transition-all duration-300 ease-in-out\\n          \".concat(mobileMenuOpen ? \"bg-black bg-opacity-50 backdrop-blur-sm visible\" : \"bg-transparent invisible\", \"\\n        \"),\n                onClick: handleBackdropClick,\n                onTouchStart: handleTouchStart,\n                onTouchMove: handleTouchMove,\n                onTouchEnd: handleTouchEnd\n            }, void 0, false, {\n                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 438,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                className: \"\\n          \".concat(mobileMenuOpen ? \"translate-x-0 shadow-2xl\" : \"-translate-x-full shadow-none\", \"\\n          lg:translate-x-0 lg:shadow-none\\n          fixed lg:relative\\n          z-50 lg:z-auto\\n          bg-slate-900\\n          border-r border-slate-700\\n          flex flex-col\\n          transition-all duration-300 ease-out\\n          \").concat(sidebarCollapsed ? \"lg:w-16\" : \"lg:w-64\", \"\\n          w-64\\n          h-screen\\n          lg:transform-none\\n        \"),\n                onTouchStart: handleTouchStart,\n                onTouchMove: handleTouchMove,\n                onTouchEnd: handleTouchEnd,\n                children: [\n                    (!sidebarCollapsed || mobileMenuOpen) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"border-b border-slate-700/50 p-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-lg font-semibold text-white\",\n                                        children: \"管理系统\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 475,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"sm\",\n                                                onClick: toggleSidebar,\n                                                className: \"hidden lg:flex text-slate-400 hover:bg-slate-800/80 hover:text-white rounded-lg transition-all duration-200\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 483,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 477,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"sm\",\n                                                onClick: toggleMobileMenu,\n                                                className: \"lg:hidden text-slate-400 hover:bg-slate-800/80 hover:text-white rounded-lg transition-all duration-200\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 491,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 485,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 476,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 474,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 p-2.5 bg-slate-800/60 rounded-lg border border-slate-700/30\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-7 h-7 bg-blue-600 rounded-lg flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            className: \"h-3.5 w-3.5 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 499,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 498,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 min-w-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-white truncate\",\n                                                children: username || \"管理员\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 502,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-slate-400\",\n                                                children: \"系统管理员\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 503,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 501,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 497,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 473,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"flex-1 overflow-y-auto \".concat(sidebarCollapsed ? 'py-2' : 'py-3'),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                            className: \"space-y-1\",\n                            children: menuItems.map((item)=>{\n                                var _item_children, _item_children1;\n                                const IconComponent = item.icon;\n                                const isActive = item.isActive || selectedView === item.id;\n                                const hasChildren = item.type === \"group\" && item.children;\n                                const isExpanded = expandedGroups.has(item.id);\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    className: \"relative\",\n                                    children: sidebarCollapsed ? /* Collapsed view - icon only with tooltip */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative group\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleMenuItemClick(item),\n                                                className: \"\\n                          w-full h-12 flex items-center justify-center rounded-lg\\n                          transition-all duration-200 mx-2\\n                          \".concat(isActive ? \"bg-blue-600 text-white\" : \"text-slate-400 hover:bg-slate-800 hover:text-white\", \"\\n                        \"),\n                                                title: item.label,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 535,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 523,\n                                                columnNumber: 23\n                                            }, this),\n                                            hasChildren && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"hidden lg:block absolute left-full top-0 ml-2 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50 pointer-events-none group-hover:pointer-events-auto\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-slate-800 border border-slate-700 rounded-lg shadow-xl min-w-[180px] py-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"px-3 py-2 border-b border-slate-700\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium text-white\",\n                                                                children: item.label\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 543,\n                                                                columnNumber: 31\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 542,\n                                                            columnNumber: 29\n                                                        }, this),\n                                                        (_item_children = item.children) === null || _item_children === void 0 ? void 0 : _item_children.map((child)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>handleSubItemClick(child),\n                                                                className: \"w-full text-left px-3 py-2 text-sm text-slate-300 hover:bg-slate-700 hover:text-white transition-colors\",\n                                                                children: child.label\n                                                            }, child.id, false, {\n                                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 546,\n                                                                columnNumber: 31\n                                                            }, this))\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 541,\n                                                    columnNumber: 27\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 540,\n                                                columnNumber: 25\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 522,\n                                        columnNumber: 21\n                                    }, this) : /* Expanded view */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleMenuItemClick(item),\n                                                className: \"\\n                          w-full flex items-center px-3 py-3 rounded-lg text-sm font-medium\\n                          transition-all duration-200 group\\n                          \".concat(isActive ? \"bg-blue-600 text-white\" : \"text-slate-300 hover:bg-slate-800 hover:text-white\", \"\\n                        \"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                        className: \"h-5 w-5 mr-3 flex-shrink-0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 572,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"flex-1 text-left\",\n                                                        children: item.label\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 573,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    hasChildren && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"h-4 w-4 transition-transform duration-200 \".concat(isExpanded ? 'rotate-90' : '')\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 575,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 561,\n                                                columnNumber: 23\n                                            }, this),\n                                            hasChildren && isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"mt-1 ml-6 space-y-1\",\n                                                children: (_item_children1 = item.children) === null || _item_children1 === void 0 ? void 0 : _item_children1.map((child)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>handleSubItemClick(child),\n                                                            className: \"\\n                                  w-full text-left px-3 py-2 rounded-lg text-sm\\n                                  transition-all duration-200\\n                                  \".concat(selectedView === child.id ? \"bg-blue-600 text-white\" : \"text-slate-400 hover:bg-slate-800 hover:text-white\", \"\\n                                \"),\n                                                            children: child.label\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 588,\n                                                            columnNumber: 31\n                                                        }, this)\n                                                    }, child.id, false, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 587,\n                                                        columnNumber: 29\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 585,\n                                                columnNumber: 25\n                                            }, this)\n                                        ]\n                                    }, void 0, true)\n                                }, item.id, false, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 519,\n                                    columnNumber: 17\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 511,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 510,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"border-t border-slate-700/50 \".concat(sidebarCollapsed ? 'p-2' : 'p-3'),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"ghost\",\n                            onClick: handleLogout,\n                            className: \"\\n              w-full text-slate-300 hover:bg-slate-800/60 hover:text-white rounded-lg\\n              transition-all duration-200 min-h-[36px]\\n              \".concat(sidebarCollapsed ? \"justify-center px-2 py-2\" : \"justify-start px-2.5 py-2\", \"\\n            \"),\n                            title: sidebarCollapsed ? \"退出登录\" : undefined,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 624,\n                                    columnNumber: 13\n                                }, this),\n                                !sidebarCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"ml-2 text-sm\",\n                                    children: \"退出登录\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 625,\n                                    columnNumber: 35\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 614,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 613,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 452,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                variant: \"ghost\",\n                size: \"sm\",\n                onClick: toggleMobileMenu,\n                className: \"\\n          lg:hidden fixed top-3 left-3 z-30\\n          bg-slate-900/95 backdrop-blur-sm text-white\\n          hover:bg-slate-800/90 active:bg-slate-700\\n          border border-slate-700/40 rounded-lg\\n          transition-all duration-200 ease-in-out\\n          hover:scale-105 active:scale-95\\n          shadow-md hover:shadow-lg\\n          min-h-[36px] min-w-[36px]\\n          \".concat(mobileMenuOpen ? 'bg-slate-800/90 scale-105' : '', \"\\n        \"),\n                onTouchStart: handleTouchStart,\n                onTouchMove: handleTouchMove,\n                onTouchEnd: handleTouchEnd,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"transition-transform duration-200 \".concat(mobileMenuOpen ? 'rotate-90' : ''),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 651,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 650,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 631,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"flex-1 p-3 lg:p-4 overflow-auto\",\n                children: selectedView === \"home\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg border border-slate-200 p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-xl font-bold text-slate-900 mb-1\",\n                                    children: [\n                                        \"欢迎回来，\",\n                                        username || \"管理员\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 660,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-slate-600\",\n                                    children: \"系统管理中心概览\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 661,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 659,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-3\",\n                            children: dashboardStats.map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                    className: \"bg-white border-slate-200 hover:shadow-lg transition-shadow\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                        className: \"p-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs font-medium text-slate-600\",\n                                                                children: stat.title\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 671,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-lg font-bold text-slate-900 mt-1\",\n                                                                children: stat.value\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 672,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-slate-500\",\n                                                                children: stat.subtitle\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 673,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 670,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-10 h-10 \".concat(stat.color, \" rounded-lg flex items-center justify-center\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-5 h-5 bg-white rounded opacity-80\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 676,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 675,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 669,\n                                                columnNumber: 21\n                                            }, this),\n                                            stat.trend && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-2 flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs font-medium \".concat(stat.trendDirection === 'up' ? 'text-green-600' : 'text-red-600'),\n                                                        children: [\n                                                            stat.trendDirection === 'up' ? '↗' : '↘',\n                                                            \" \",\n                                                            stat.trend\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 681,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-slate-500 ml-1\",\n                                                        children: \"vs 昨天\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 686,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 680,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 668,\n                                        columnNumber: 19\n                                    }, this)\n                                }, index, false, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 667,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 665,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                    className: \"bg-white border-slate-200 hover:shadow-lg transition-shadow\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                            className: \"pb-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                className: \"text-slate-900 flex items-center gap-2 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                        className: \"h-4 w-4 text-blue-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 700,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Agent 性能\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 699,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 698,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                            className: \"space-y-2\",\n                                            children: performanceData.agentPerformance.slice(0, 3).map((agent, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between p-2 bg-slate-50 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1 min-w-0\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs font-medium text-slate-900 truncate\",\n                                                                    children: agent.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 708,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-slate-500\",\n                                                                    children: [\n                                                                        \"成功率: \",\n                                                                        agent.success,\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 709,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 707,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-right\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm font-bold text-blue-600\",\n                                                                    children: agent.completed\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 712,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-slate-500\",\n                                                                    children: \"任务\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 713,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 711,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 706,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 704,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 697,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                    className: \"bg-white border-slate-200 hover:shadow-lg transition-shadow\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                            className: \"pb-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                className: \"text-slate-900 flex items-center gap-2 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                        className: \"h-4 w-4 text-purple-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 724,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"系统负载\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 723,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 722,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                            className: \"space-y-2\",\n                                            children: performanceData.systemLoad.slice(-3).map((load, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between text-xs\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-slate-600\",\n                                                                    children: load.time\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 732,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-slate-900 font-medium\",\n                                                                    children: [\n                                                                        \"CPU: \",\n                                                                        load.cpu,\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 733,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 731,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-full bg-slate-200 rounded-full h-1.5\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-purple-600 h-1.5 rounded-full transition-all duration-300\",\n                                                                style: {\n                                                                    width: \"\".concat(load.cpu, \"%\")\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 736,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 735,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 730,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 728,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 721,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                    className: \"bg-white border-slate-200 md:col-span-2 xl:col-span-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                            className: \"pb-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                className: \"text-slate-900 flex items-center gap-2 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                        className: \"h-4 w-4 text-orange-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 750,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"最近活动\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 749,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 748,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                            className: \"space-y-2\",\n                                            children: agentActivity.slice(0, 3).map((activity)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2 p-2 bg-slate-50 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 rounded-full \".concat(activity.color)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 757,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1 min-w-0\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs font-medium text-slate-900 truncate\",\n                                                                    children: activity.agent\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 759,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-slate-600 truncate\",\n                                                                    children: activity.action\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 760,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 758,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-right\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs font-medium \".concat(activity.status === '已完成' ? 'text-green-600' : activity.status === '进行中' ? 'text-blue-600' : 'text-yellow-600'),\n                                                                children: activity.status\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 763,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 762,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, activity.id, true, {\n                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 756,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 754,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 747,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 695,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            className: \"bg-white border-slate-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                    className: \"pb-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                        className: \"text-slate-900 flex items-center gap-2 text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                className: \"h-4 w-4 text-indigo-600\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 780,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Agent 状态总览\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 779,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 778,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-2\",\n                                        children: agents.map((agent)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between p-2 bg-slate-50 rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-2 h-2 rounded-full \".concat(agent.status === '在线' ? 'bg-green-500' : agent.status === '忙碌' ? 'bg-yellow-500' : 'bg-gray-400')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 789,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"min-w-0\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs font-medium text-slate-900 truncate\",\n                                                                        children: agent.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 794,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-slate-500\",\n                                                                        children: agent.type\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 795,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 793,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 788,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-right\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs font-medium text-slate-900\",\n                                                            children: agent.tasksCompleted\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 799,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 798,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, agent.id, true, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 787,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 785,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 784,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 777,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 657,\n                    columnNumber: 11\n                }, this) : selectedView === \"apps\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg border border-slate-200 p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-xl font-bold text-slate-900 mb-1\",\n                                    children: \"应用管理\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 811,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-slate-600\",\n                                    children: \"管理和监控系统应用\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 812,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 810,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4\",\n                            children: agents.map((agent)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                    className: \"bg-white border-slate-200 hover:shadow-lg transition-shadow cursor-pointer\",\n                                    onClick: ()=>handleViewSelect(agent.id),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                        className: \"p-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center gap-3 mb-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-3 h-3 rounded-full \".concat(agent.status === '在线' ? 'bg-green-500' : agent.status === '忙碌' ? 'bg-yellow-500' : 'bg-gray-400')\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 821,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm font-medium text-slate-600\",\n                                                        children: agent.type\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 825,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 820,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-semibold text-slate-900 mb-2\",\n                                                children: agent.name\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 827,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-1 text-sm text-slate-600\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: [\n                                                            \"状态: \",\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium \".concat(agent.status === '在线' ? 'text-green-600' : agent.status === '忙碌' ? 'text-yellow-600' : 'text-gray-600'),\n                                                                children: agent.status\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 829,\n                                                                columnNumber: 30\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 829,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: [\n                                                            \"最后活动: \",\n                                                            agent.lastActive\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 833,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: [\n                                                            \"完成任务: \",\n                                                            agent.tasksCompleted\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 834,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: [\n                                                            \"成功率: \",\n                                                            agent.successRate,\n                                                            \"%\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 835,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 828,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 819,\n                                        columnNumber: 19\n                                    }, this)\n                                }, agent.id, false, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 818,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 816,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 808,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                    className: \"h-full bg-white border-slate-200\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        className: \"h-full p-0\",\n                        children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-full flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                        className: \"h-8 w-8 animate-spin text-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 848,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-slate-600\",\n                                        children: \"加载中...\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 849,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 847,\n                                columnNumber: 19\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 846,\n                            columnNumber: 17\n                        }, this) : iframeError ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-full flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center gap-4 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                        className: \"h-12 w-12 text-red-500\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 855,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-slate-900 font-medium\",\n                                                children: \"加载失败\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 857,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-slate-600 text-sm mt-1\",\n                                                children: \"无法加载内容\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 858,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 856,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        onClick: ()=>handleViewSelect(selectedView),\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        children: \"重试\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 860,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 854,\n                                columnNumber: 19\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 853,\n                            columnNumber: 17\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-full flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"h-16 w-16 text-slate-400 mx-auto mb-4\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 868,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-semibold text-slate-900 mb-2\",\n                                        children: ((_menuItems_find = menuItems.find((item)=>item.id === selectedView)) === null || _menuItems_find === void 0 ? void 0 : _menuItems_find.label) || \"功能\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 869,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-slate-600\",\n                                        children: \"此功能正在开发中...\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 872,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 867,\n                                columnNumber: 19\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 866,\n                            columnNumber: 17\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 844,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 843,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 655,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 436,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardPage, \"WP+xSObwB6TJowbL3Zc/sms9bro=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = DashboardPage;\nvar _c;\n$RefreshReg$(_c, \"DashboardPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/dashboard/page.tsx\n"));

/***/ })

});