"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/page",{

/***/ "(app-pages-browser)/./app/dashboard/page.tsx":
/*!********************************!*\
  !*** ./app/dashboard/page.tsx ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.2.4_react-dom@19.1.1_react@19.1.1__react@19.1.1/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,Calendar,ChevronLeft,ChevronRight,Container,Database,FileText,Globe,Home,LineChart,Loader2,LogOut,Menu,Package,Server,Settings,Terminal,User,Users,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,Calendar,ChevronLeft,ChevronRight,Container,Database,FileText,Globe,Home,LineChart,Loader2,LogOut,Menu,Package,Server,Settings,Terminal,User,Users,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/package.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,Calendar,ChevronLeft,ChevronRight,Container,Database,FileText,Globe,Home,LineChart,Loader2,LogOut,Menu,Package,Server,Settings,Terminal,User,Users,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,Calendar,ChevronLeft,ChevronRight,Container,Database,FileText,Globe,Home,LineChart,Loader2,LogOut,Menu,Package,Server,Settings,Terminal,User,Users,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,Calendar,ChevronLeft,ChevronRight,Container,Database,FileText,Globe,Home,LineChart,Loader2,LogOut,Menu,Package,Server,Settings,Terminal,User,Users,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,Calendar,ChevronLeft,ChevronRight,Container,Database,FileText,Globe,Home,LineChart,Loader2,LogOut,Menu,Package,Server,Settings,Terminal,User,Users,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/container.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,Calendar,ChevronLeft,ChevronRight,Container,Database,FileText,Globe,Home,LineChart,Loader2,LogOut,Menu,Package,Server,Settings,Terminal,User,Users,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/server.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,Calendar,ChevronLeft,ChevronRight,Container,Database,FileText,Globe,Home,LineChart,Loader2,LogOut,Menu,Package,Server,Settings,Terminal,User,Users,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/terminal.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,Calendar,ChevronLeft,ChevronRight,Container,Database,FileText,Globe,Home,LineChart,Loader2,LogOut,Menu,Package,Server,Settings,Terminal,User,Users,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,Calendar,ChevronLeft,ChevronRight,Container,Database,FileText,Globe,Home,LineChart,Loader2,LogOut,Menu,Package,Server,Settings,Terminal,User,Users,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/wrench.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,Calendar,ChevronLeft,ChevronRight,Container,Database,FileText,Globe,Home,LineChart,Loader2,LogOut,Menu,Package,Server,Settings,Terminal,User,Users,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,Calendar,ChevronLeft,ChevronRight,Container,Database,FileText,Globe,Home,LineChart,Loader2,LogOut,Menu,Package,Server,Settings,Terminal,User,Users,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,Calendar,ChevronLeft,ChevronRight,Container,Database,FileText,Globe,Home,LineChart,Loader2,LogOut,Menu,Package,Server,Settings,Terminal,User,Users,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,Calendar,ChevronLeft,ChevronRight,Container,Database,FileText,Globe,Home,LineChart,Loader2,LogOut,Menu,Package,Server,Settings,Terminal,User,Users,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,Calendar,ChevronLeft,ChevronRight,Container,Database,FileText,Globe,Home,LineChart,Loader2,LogOut,Menu,Package,Server,Settings,Terminal,User,Users,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,Calendar,ChevronLeft,ChevronRight,Container,Database,FileText,Globe,Home,LineChart,Loader2,LogOut,Menu,Package,Server,Settings,Terminal,User,Users,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,Calendar,ChevronLeft,ChevronRight,Container,Database,FileText,Globe,Home,LineChart,Loader2,LogOut,Menu,Package,Server,Settings,Terminal,User,Users,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,Calendar,ChevronLeft,ChevronRight,Container,Database,FileText,Globe,Home,LineChart,Loader2,LogOut,Menu,Package,Server,Settings,Terminal,User,Users,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,Calendar,ChevronLeft,ChevronRight,Container,Database,FileText,Globe,Home,LineChart,Loader2,LogOut,Menu,Package,Server,Settings,Terminal,User,Users,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,Calendar,ChevronLeft,ChevronRight,Container,Database,FileText,Globe,Home,LineChart,Loader2,LogOut,Menu,Package,Server,Settings,Terminal,User,Users,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/chart-line.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,Calendar,ChevronLeft,ChevronRight,Container,Database,FileText,Globe,Home,LineChart,Loader2,LogOut,Menu,Package,Server,Settings,Terminal,User,Users,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,Calendar,ChevronLeft,ChevronRight,Container,Database,FileText,Globe,Home,LineChart,Loader2,LogOut,Menu,Package,Server,Settings,Terminal,User,Users,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,Calendar,ChevronLeft,ChevronRight,Container,Database,FileText,Globe,Home,LineChart,Loader2,LogOut,Menu,Package,Server,Settings,Terminal,User,Users,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertCircle,BarChart3,Brain,Calendar,ChevronLeft,ChevronRight,Container,Database,FileText,Globe,Home,LineChart,Loader2,LogOut,Menu,Package,Server,Settings,Terminal,User,Users,Wrench,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n// Menu configuration matching the reference design\nconst menuItems = [\n    {\n        id: \"home\",\n        icon: _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        label: \"首页\",\n        type: \"single\"\n    },\n    {\n        id: \"apps\",\n        icon: _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        label: \"应用\",\n        type: \"single\",\n        isActive: true // This represents the current active menu item\n    },\n    {\n        id: \"website\",\n        icon: _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        label: \"网站\",\n        type: \"group\",\n        children: [\n            {\n                id: \"website-1\",\n                label: \"网站管理\",\n                url: \"https://example.com/website1\"\n            },\n            {\n                id: \"website-2\",\n                label: \"域名管理\",\n                url: \"https://example.com/website2\"\n            },\n            {\n                id: \"website-3\",\n                label: \"SSL证书\",\n                url: \"https://example.com/website3\"\n            }\n        ]\n    },\n    {\n        id: \"ai\",\n        icon: _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        label: \"AI\",\n        type: \"group\",\n        children: [\n            {\n                id: \"ai-chat\",\n                label: \"智能对话\",\n                url: \"https://example.com/ai-chat\"\n            },\n            {\n                id: \"ai-analysis\",\n                label: \"数据分析\",\n                url: \"https://example.com/ai-analysis\"\n            },\n            {\n                id: \"ai-content\",\n                label: \"内容生成\",\n                url: \"https://example.com/ai-content\"\n            }\n        ]\n    },\n    {\n        id: \"database\",\n        icon: _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        label: \"数据库\",\n        type: \"single\"\n    },\n    {\n        id: \"docker\",\n        icon: _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        label: \"容器\",\n        type: \"single\"\n    },\n    {\n        id: \"system\",\n        icon: _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n        label: \"系统\",\n        type: \"group\",\n        children: [\n            {\n                id: \"system-monitor\",\n                label: \"系统监控\",\n                url: \"https://example.com/monitor\"\n            },\n            {\n                id: \"system-logs\",\n                label: \"系统日志\",\n                url: \"https://example.com/logs\"\n            },\n            {\n                id: \"system-users\",\n                label: \"用户管理\",\n                url: \"https://example.com/users\"\n            }\n        ]\n    },\n    {\n        id: \"terminal\",\n        icon: _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n        label: \"终端\",\n        type: \"single\"\n    },\n    {\n        id: \"schedule\",\n        icon: _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n        label: \"计划\",\n        type: \"single\"\n    },\n    {\n        id: \"tools\",\n        icon: _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n        label: \"工具\",\n        type: \"single\"\n    },\n    {\n        id: \"advanced\",\n        icon: _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        label: \"高级功能\",\n        type: \"group\",\n        children: [\n            {\n                id: \"advanced-backup\",\n                label: \"备份管理\",\n                url: \"https://example.com/backup\"\n            },\n            {\n                id: \"advanced-security\",\n                label: \"安全设置\",\n                url: \"https://example.com/security\"\n            },\n            {\n                id: \"advanced-api\",\n                label: \"API管理\",\n                url: \"https://example.com/api\"\n            }\n        ]\n    },\n    {\n        id: \"logs\",\n        icon: _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n        label: \"日志\",\n        type: \"single\"\n    },\n    {\n        id: \"settings\",\n        icon: _barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n        label: \"设置\",\n        type: \"single\"\n    }\n];\n// Enhanced mock data for realistic demonstration\nconst agents = [\n    {\n        id: \"agent1\",\n        name: \"智能客服助手\",\n        url: \"https://example.com/agent1\",\n        type: \"客服\",\n        status: \"在线\",\n        lastActive: \"2分钟前\",\n        tasksCompleted: 127,\n        successRate: 98.5\n    },\n    {\n        id: \"agent2\",\n        name: \"数据分析专家\",\n        url: \"https://example.com/agent2\",\n        type: \"分析\",\n        status: \"忙碌\",\n        lastActive: \"刚刚\",\n        tasksCompleted: 89,\n        successRate: 96.2\n    },\n    {\n        id: \"agent3\",\n        name: \"内容创作助理\",\n        url: \"https://example.com/agent3\",\n        type: \"创作\",\n        status: \"在线\",\n        lastActive: \"5分钟前\",\n        tasksCompleted: 156,\n        successRate: 94.8\n    },\n    {\n        id: \"agent4\",\n        name: \"代码审查机器人\",\n        url: \"https://example.com/agent4\",\n        type: \"开发\",\n        status: \"离线\",\n        lastActive: \"1小时前\",\n        tasksCompleted: 73,\n        successRate: 99.1\n    }\n];\nconst dashboardStats = [\n    {\n        title: \"总代理\",\n        value: \"4\",\n        subtitle: \"3个在线\",\n        color: \"bg-blue-500\",\n        trend: \"+2\",\n        trendDirection: \"up\"\n    },\n    {\n        title: \"CPU使用率\",\n        value: \"42.8%\",\n        subtitle: \"平均负载\",\n        color: \"bg-green-500\",\n        trend: \"-5.2%\",\n        trendDirection: \"down\"\n    },\n    {\n        title: \"内存使用\",\n        value: \"67.3%\",\n        subtitle: \"8.2GB / 12GB\",\n        color: \"bg-purple-500\",\n        trend: \"+3.1%\",\n        trendDirection: \"up\"\n    },\n    {\n        title: \"今日任务\",\n        value: \"445\",\n        subtitle: \"已完成\",\n        color: \"bg-orange-500\",\n        trend: \"+28\",\n        trendDirection: \"up\"\n    }\n];\nconst agentActivity = [\n    {\n        id: 1,\n        agent: \"智能客服助手\",\n        action: \"处理用户咨询\",\n        status: \"已完成\",\n        time: \"刚刚\",\n        color: \"bg-green-500\",\n        duration: \"2分钟\"\n    },\n    {\n        id: 2,\n        agent: \"数据分析专家\",\n        action: \"生成销售报告\",\n        status: \"进行中\",\n        time: \"3分钟前\",\n        color: \"bg-blue-500\",\n        duration: \"预计5分钟\"\n    },\n    {\n        id: 3,\n        agent: \"内容创作助理\",\n        action: \"撰写产品描述\",\n        status: \"已完成\",\n        time: \"5分钟前\",\n        color: \"bg-green-500\",\n        duration: \"8分钟\"\n    },\n    {\n        id: 4,\n        agent: \"智能客服助手\",\n        action: \"更新知识库\",\n        status: \"已完成\",\n        time: \"8分钟前\",\n        color: \"bg-green-500\",\n        duration: \"3分钟\"\n    },\n    {\n        id: 5,\n        agent: \"代码审查机器人\",\n        action: \"代码质量检查\",\n        status: \"等待中\",\n        time: \"12分钟前\",\n        color: \"bg-yellow-500\",\n        duration: \"待处理\"\n    }\n];\n// Performance metrics for charts\nconst performanceData = {\n    systemLoad: [\n        {\n            time: \"00:00\",\n            cpu: 35,\n            memory: 62,\n            network: 45\n        },\n        {\n            time: \"04:00\",\n            cpu: 28,\n            memory: 58,\n            network: 38\n        },\n        {\n            time: \"08:00\",\n            cpu: 42,\n            memory: 65,\n            network: 52\n        },\n        {\n            time: \"12:00\",\n            cpu: 48,\n            memory: 71,\n            network: 61\n        },\n        {\n            time: \"16:00\",\n            cpu: 38,\n            memory: 67,\n            network: 47\n        },\n        {\n            time: \"20:00\",\n            cpu: 33,\n            memory: 63,\n            network: 42\n        }\n    ],\n    agentPerformance: [\n        {\n            name: \"智能客服助手\",\n            completed: 127,\n            success: 98.5,\n            avgTime: 3.2\n        },\n        {\n            name: \"数据分析专家\",\n            completed: 89,\n            success: 96.2,\n            avgTime: 12.5\n        },\n        {\n            name: \"内容创作助理\",\n            completed: 156,\n            success: 94.8,\n            avgTime: 8.7\n        },\n        {\n            name: \"代码审查机器人\",\n            completed: 73,\n            success: 99.1,\n            avgTime: 15.3\n        }\n    ],\n    taskDistribution: [\n        {\n            category: \"客服咨询\",\n            count: 185,\n            percentage: 41.6\n        },\n        {\n            category: \"数据分析\",\n            count: 89,\n            percentage: 20.0\n        },\n        {\n            category: \"内容创作\",\n            count: 156,\n            percentage: 35.1\n        },\n        {\n            category: \"代码审查\",\n            count: 15,\n            percentage: 3.3\n        }\n    ]\n};\nfunction DashboardPage() {\n    _s();\n    const [selectedView, setSelectedView] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"apps\") // Default to apps view to match reference\n    ;\n    const [username, setUsername] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [sidebarCollapsed, setSidebarCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true) // Default to collapsed like reference\n    ;\n    const [mobileMenuOpen, setMobileMenuOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [touchStartX, setTouchStartX] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [touchCurrentX, setTouchCurrentX] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [iframeError, setIframeError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [expandedGroups, setExpandedGroups] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set()) // Track expanded menu groups\n    ;\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DashboardPage.useEffect\": ()=>{\n            const isLoggedIn = localStorage.getItem(\"isLoggedIn\");\n            const storedUsername = localStorage.getItem(\"username\");\n            if (!isLoggedIn) {\n                router.push(\"/\");\n                return;\n            }\n            if (storedUsername) {\n                setUsername(storedUsername);\n            }\n        }\n    }[\"DashboardPage.useEffect\"], [\n        router\n    ]);\n    // Enhanced mobile menu accessibility and keyboard support\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"DashboardPage.useEffect\": ()=>{\n            const handleKeyDown = {\n                \"DashboardPage.useEffect.handleKeyDown\": (e)=>{\n                    if (e.key === 'Escape' && mobileMenuOpen) {\n                        setMobileMenuOpen(false);\n                    }\n                }\n            }[\"DashboardPage.useEffect.handleKeyDown\"];\n            const handleResize = {\n                \"DashboardPage.useEffect.handleResize\": ()=>{\n                    // Close mobile menu when switching to desktop view\n                    if (window.innerWidth >= 1024 && mobileMenuOpen) {\n                        setMobileMenuOpen(false);\n                    }\n                }\n            }[\"DashboardPage.useEffect.handleResize\"];\n            document.addEventListener('keydown', handleKeyDown);\n            window.addEventListener('resize', handleResize);\n            return ({\n                \"DashboardPage.useEffect\": ()=>{\n                    document.removeEventListener('keydown', handleKeyDown);\n                    window.removeEventListener('resize', handleResize);\n                }\n            })[\"DashboardPage.useEffect\"];\n        }\n    }[\"DashboardPage.useEffect\"], [\n        mobileMenuOpen\n    ]);\n    const handleLogout = ()=>{\n        localStorage.removeItem(\"isLoggedIn\");\n        localStorage.removeItem(\"username\");\n        router.push(\"/\");\n    };\n    const toggleSidebar = ()=>{\n        setSidebarCollapsed(!sidebarCollapsed);\n    };\n    const toggleMobileMenu = ()=>{\n        setMobileMenuOpen(!mobileMenuOpen);\n    };\n    // Enhanced touch handling for mobile menu\n    const handleTouchStart = (e)=>{\n        setTouchStartX(e.touches[0].clientX);\n        setTouchCurrentX(e.touches[0].clientX);\n    };\n    const handleTouchMove = (e)=>{\n        if (touchStartX === null) return;\n        setTouchCurrentX(e.touches[0].clientX);\n    };\n    const handleTouchEnd = ()=>{\n        if (touchStartX === null || touchCurrentX === null) return;\n        const deltaX = touchCurrentX - touchStartX;\n        const threshold = 50 // Minimum swipe distance\n        ;\n        // Swipe right to open menu (when closed)\n        if (deltaX > threshold && !mobileMenuOpen) {\n            setMobileMenuOpen(true);\n        } else if (deltaX < -threshold && mobileMenuOpen) {\n            setMobileMenuOpen(false);\n        }\n        setTouchStartX(null);\n        setTouchCurrentX(null);\n    };\n    // Close mobile menu when clicking outside\n    const handleBackdropClick = (e)=>{\n        e.stopPropagation();\n        setMobileMenuOpen(false);\n    };\n    const handleViewSelect = (view)=>{\n        // Handle different view types\n        if (view === \"home\" || view === \"apps\") {\n            setSelectedView(view);\n            setMobileMenuOpen(false);\n            return;\n        }\n        // For other views, simulate loading\n        setIsLoading(true);\n        setIframeError(false);\n        setSelectedView(view);\n        setMobileMenuOpen(false);\n        setTimeout(()=>setIsLoading(false), 1000);\n    };\n    const handleMenuItemClick = (item)=>{\n        if (item.type === \"single\") {\n            handleViewSelect(item.id);\n        } else if (item.type === \"group\") {\n            // Toggle group expansion when sidebar is expanded\n            if (!sidebarCollapsed) {\n                setExpandedGroups((prev)=>{\n                    const newSet = new Set(prev);\n                    if (newSet.has(item.id)) {\n                        newSet.delete(item.id);\n                    } else {\n                        newSet.add(item.id);\n                    }\n                    return newSet;\n                });\n            }\n        }\n    };\n    const handleSubItemClick = (subItem)=>{\n        setIsLoading(true);\n        setIframeError(false);\n        setSelectedView(subItem.id);\n        setMobileMenuOpen(false);\n        setTimeout(()=>setIsLoading(false), 1000);\n    };\n    const selectedAgentData = agents.find((agent)=>agent.id === selectedView);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-slate-50 flex\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"\\n          fixed inset-0 z-40 lg:hidden transition-all duration-300 ease-in-out\\n          \".concat(mobileMenuOpen ? \"bg-black bg-opacity-50 backdrop-blur-sm visible\" : \"bg-transparent invisible\", \"\\n        \"),\n                onClick: handleBackdropClick,\n                onTouchStart: handleTouchStart,\n                onTouchMove: handleTouchMove,\n                onTouchEnd: handleTouchEnd\n            }, void 0, false, {\n                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 438,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n                className: \"\\n          \".concat(mobileMenuOpen ? \"translate-x-0 shadow-2xl\" : \"-translate-x-full shadow-none\", \"\\n          lg:translate-x-0 lg:shadow-none\\n          fixed lg:relative\\n          z-50 lg:z-auto\\n          bg-slate-900\\n          border-r border-slate-700\\n          flex flex-col\\n          transition-all duration-300 ease-out\\n          \").concat(sidebarCollapsed ? \"lg:w-16\" : \"lg:w-64\", \"\\n          w-64\\n          h-screen\\n          lg:transform-none\\n        \"),\n                onTouchStart: handleTouchStart,\n                onTouchMove: handleTouchMove,\n                onTouchEnd: handleTouchEnd,\n                children: [\n                    (!sidebarCollapsed || mobileMenuOpen) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"border-b border-slate-700/50 p-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between mb-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-lg font-semibold text-white\",\n                                        children: \"管理系统\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 475,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"sm\",\n                                                onClick: toggleSidebar,\n                                                className: \"hidden lg:flex text-slate-400 hover:bg-slate-800/80 hover:text-white rounded-lg transition-all duration-200\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 483,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 477,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"sm\",\n                                                onClick: toggleMobileMenu,\n                                                className: \"lg:hidden text-slate-400 hover:bg-slate-800/80 hover:text-white rounded-lg transition-all duration-200\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 491,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 485,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 476,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 474,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2 p-2.5 bg-slate-800/60 rounded-lg border border-slate-700/30\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-7 h-7 bg-blue-600 rounded-lg flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                            className: \"h-3.5 w-3.5 text-white\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 499,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 498,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 min-w-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium text-white truncate\",\n                                                children: username || \"管理员\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 502,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-slate-400\",\n                                                children: \"系统管理员\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 503,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 501,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 497,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 473,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                        className: \"flex-1 overflow-y-auto \".concat(sidebarCollapsed ? 'py-2' : 'py-3'),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                            className: \"space-y-1\",\n                            children: menuItems.map((item)=>{\n                                var _item_children, _item_children1;\n                                const IconComponent = item.icon;\n                                const isActive = item.isActive || selectedView === item.id;\n                                const hasChildren = item.type === \"group\" && item.children;\n                                const isExpanded = expandedGroups.has(item.id);\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    className: \"relative\",\n                                    children: sidebarCollapsed ? /* Collapsed view - icon only with tooltip */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative group\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleMenuItemClick(item),\n                                                className: \"\\n                          w-full h-12 flex items-center justify-center rounded-lg\\n                          transition-all duration-200 mx-2\\n                          \".concat(isActive ? \"bg-blue-600 text-white\" : \"text-slate-400 hover:bg-slate-800 hover:text-white\", \"\\n                        \"),\n                                                title: item.label,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 535,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 523,\n                                                columnNumber: 23\n                                            }, this),\n                                            hasChildren && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"hidden lg:block absolute left-full top-0 ml-2 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50 pointer-events-none group-hover:pointer-events-auto\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-slate-800 border border-slate-700 rounded-lg shadow-xl min-w-[180px] py-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"px-3 py-2 border-b border-slate-700\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium text-white\",\n                                                                children: item.label\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 543,\n                                                                columnNumber: 31\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 542,\n                                                            columnNumber: 29\n                                                        }, this),\n                                                        (_item_children = item.children) === null || _item_children === void 0 ? void 0 : _item_children.map((child)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: ()=>handleSubItemClick(child),\n                                                                className: \"w-full text-left px-3 py-2 text-sm text-slate-300 hover:bg-slate-700 hover:text-white transition-colors\",\n                                                                children: child.label\n                                                            }, child.id, false, {\n                                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 546,\n                                                                columnNumber: 31\n                                                            }, this))\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 541,\n                                                    columnNumber: 27\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 540,\n                                                columnNumber: 25\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 522,\n                                        columnNumber: 21\n                                    }, this) : /* Expanded view */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleMenuItemClick(item),\n                                                className: \"\\n                          w-full flex items-center px-3 py-3 rounded-lg text-sm font-medium\\n                          transition-all duration-200 group\\n                          \".concat(isActive ? \"bg-blue-600 text-white\" : \"text-slate-300 hover:bg-slate-800 hover:text-white\", \"\\n                        \"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                        className: \"h-5 w-5 mr-3 flex-shrink-0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 572,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"flex-1 text-left\",\n                                                        children: item.label\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 573,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    hasChildren && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"h-4 w-4 transition-transform duration-200 \".concat(isExpanded ? 'rotate-90' : '')\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 575,\n                                                        columnNumber: 27\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 561,\n                                                columnNumber: 23\n                                            }, this),\n                                            hasChildren && isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"mt-1 ml-6 space-y-1\",\n                                                children: (_item_children1 = item.children) === null || _item_children1 === void 0 ? void 0 : _item_children1.map((child)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>handleSubItemClick(child),\n                                                            className: \"\\n                                  w-full text-left px-3 py-2 rounded-lg text-sm\\n                                  transition-all duration-200\\n                                  \".concat(selectedView === child.id ? \"bg-blue-600 text-white\" : \"text-slate-400 hover:bg-slate-800 hover:text-white\", \"\\n                                \"),\n                                                            children: child.label\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 588,\n                                                            columnNumber: 31\n                                                        }, this)\n                                                    }, child.id, false, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 587,\n                                                        columnNumber: 29\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 585,\n                                                columnNumber: 25\n                                            }, this)\n                                        ]\n                                    }, void 0, true)\n                                }, item.id, false, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 519,\n                                    columnNumber: 17\n                                }, this);\n                            })\n                        }, void 0, false, {\n                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 511,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 510,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"border-t border-slate-700/50 \".concat(sidebarCollapsed ? 'p-2' : 'p-3'),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"ghost\",\n                            onClick: handleLogout,\n                            className: \"\\n              w-full text-slate-300 hover:bg-slate-800/60 hover:text-white rounded-lg\\n              transition-all duration-200 min-h-[36px]\\n              \".concat(sidebarCollapsed ? \"justify-center px-2 py-2\" : \"justify-start px-2.5 py-2\", \"\\n            \"),\n                            title: sidebarCollapsed ? \"退出登录\" : undefined,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 624,\n                                    columnNumber: 13\n                                }, this),\n                                !sidebarCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"ml-2 text-sm\",\n                                    children: \"退出登录\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 625,\n                                    columnNumber: 35\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 614,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 613,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 452,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                variant: \"ghost\",\n                size: \"sm\",\n                onClick: toggleMobileMenu,\n                className: \"\\n          lg:hidden fixed top-3 left-3 z-30\\n          bg-slate-900/95 backdrop-blur-sm text-white\\n          hover:bg-slate-800/90 active:bg-slate-700\\n          border border-slate-700/40 rounded-lg\\n          transition-all duration-200 ease-in-out\\n          hover:scale-105 active:scale-95\\n          shadow-md hover:shadow-lg\\n          min-h-[36px] min-w-[36px]\\n          \".concat(mobileMenuOpen ? 'bg-slate-800/90 scale-105' : '', \"\\n        \"),\n                onTouchStart: handleTouchStart,\n                onTouchMove: handleTouchMove,\n                onTouchEnd: handleTouchEnd,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"transition-transform duration-200 \".concat(mobileMenuOpen ? 'rotate-90' : ''),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 651,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 650,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 631,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"flex-1 p-3 lg:p-4 overflow-auto\",\n                children: selectedView === \"home\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-lg border border-slate-200 p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-xl font-bold text-slate-900 mb-1\",\n                                    children: [\n                                        \"欢迎回来，\",\n                                        username || \"管理员\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 660,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-slate-600\",\n                                    children: \"AI Agent 管理中心概览\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 661,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 659,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-3\",\n                            children: dashboardStats.map((stat, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                    className: \"bg-white border-slate-200 hover:shadow-lg transition-shadow\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                        className: \"p-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs font-medium text-slate-600\",\n                                                                children: stat.title\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 671,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-lg font-bold text-slate-900 mt-1\",\n                                                                children: stat.value\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 672,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs text-slate-500\",\n                                                                children: stat.subtitle\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 673,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 670,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-10 h-10 \".concat(stat.color, \" rounded-lg flex items-center justify-center\"),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-5 h-5 bg-white rounded opacity-80\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 676,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 675,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 669,\n                                                columnNumber: 21\n                                            }, this),\n                                            stat.trend && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-2 flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs font-medium \".concat(stat.trendDirection === 'up' ? 'text-green-600' : 'text-red-600'),\n                                                        children: [\n                                                            stat.trendDirection === 'up' ? '↗' : '↘',\n                                                            \" \",\n                                                            stat.trend\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 681,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-slate-500 ml-1\",\n                                                        children: \"vs 昨天\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 686,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 680,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 668,\n                                        columnNumber: 19\n                                    }, this)\n                                }, index, false, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 667,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 665,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                    className: \"bg-white border-slate-200 hover:shadow-lg transition-shadow\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                            className: \"pb-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                className: \"text-slate-900 flex items-center gap-2 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                        className: \"h-4 w-4 text-blue-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 700,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Agent 性能\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 699,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 698,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                            className: \"space-y-2\",\n                                            children: performanceData.agentPerformance.slice(0, 3).map((agent, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between p-2 bg-slate-50 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1 min-w-0\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs font-medium text-slate-900 truncate\",\n                                                                    children: agent.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 708,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-slate-500\",\n                                                                    children: [\n                                                                        \"成功率: \",\n                                                                        agent.success,\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 709,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 707,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-right\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm font-bold text-blue-600\",\n                                                                    children: agent.completed\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 712,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-slate-500\",\n                                                                    children: \"任务\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 713,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 711,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 706,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 704,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 697,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                    className: \"bg-white border-slate-200 hover:shadow-lg transition-shadow\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                            className: \"pb-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                className: \"text-slate-900 flex items-center gap-2 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                        className: \"h-4 w-4 text-purple-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 724,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"系统负载\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 723,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 722,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                            className: \"space-y-2\",\n                                            children: performanceData.systemLoad.slice(-3).map((load, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex justify-between text-xs\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-slate-600\",\n                                                                    children: load.time\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 732,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-slate-900 font-medium\",\n                                                                    children: [\n                                                                        \"CPU: \",\n                                                                        load.cpu,\n                                                                        \"%\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 733,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 731,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-full bg-slate-200 rounded-full h-1.5\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-purple-600 h-1.5 rounded-full transition-all duration-300\",\n                                                                style: {\n                                                                    width: \"\".concat(load.cpu, \"%\")\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 736,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 735,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 730,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 728,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 721,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                    className: \"bg-white border-slate-200 md:col-span-2 xl:col-span-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                            className: \"pb-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                className: \"text-slate-900 flex items-center gap-2 text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                        className: \"h-4 w-4 text-orange-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 750,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"最近活动\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 749,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 748,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                            className: \"space-y-2\",\n                                            children: agentActivity.slice(0, 3).map((activity)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2 p-2 bg-slate-50 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 rounded-full \".concat(activity.color)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 757,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1 min-w-0\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs font-medium text-slate-900 truncate\",\n                                                                    children: activity.agent\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 759,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs text-slate-600 truncate\",\n                                                                    children: activity.action\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 760,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 758,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-right\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-xs font-medium \".concat(activity.status === '已完成' ? 'text-green-600' : activity.status === '进行中' ? 'text-blue-600' : 'text-yellow-600'),\n                                                                children: activity.status\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 763,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 762,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, activity.id, true, {\n                                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 756,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 754,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 747,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 695,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                            className: \"bg-white border-slate-200\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                    className: \"pb-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                        className: \"text-slate-900 flex items-center gap-2 text-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                className: \"h-4 w-4 text-indigo-600\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 780,\n                                                columnNumber: 19\n                                            }, this),\n                                            \"Agent 状态总览\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 779,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 778,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-2\",\n                                        children: agents.map((agent)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between p-2 bg-slate-50 rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-2 h-2 rounded-full \".concat(agent.status === '在线' ? 'bg-green-500' : agent.status === '忙碌' ? 'bg-yellow-500' : 'bg-gray-400')\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 789,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"min-w-0\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs font-medium text-slate-900 truncate\",\n                                                                        children: agent.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 794,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-slate-500\",\n                                                                        children: agent.type\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 795,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 793,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 788,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-right\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-xs font-medium text-slate-900\",\n                                                            children: agent.tasksCompleted\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 799,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 798,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, agent.id, true, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 787,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 785,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 784,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 777,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 657,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                    className: \"h-full bg-white border-slate-200\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                        className: \"h-full p-0\",\n                        children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-full flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                        className: \"h-8 w-8 animate-spin text-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 813,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-slate-600\",\n                                        children: [\n                                            \"加载 \",\n                                            selectedAgentData === null || selectedAgentData === void 0 ? void 0 : selectedAgentData.name,\n                                            \"...\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 814,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 812,\n                                columnNumber: 19\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 811,\n                            columnNumber: 17\n                        }, this) : iframeError ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-full flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center gap-4 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertCircle_BarChart3_Brain_Calendar_ChevronLeft_ChevronRight_Container_Database_FileText_Globe_Home_LineChart_Loader2_LogOut_Menu_Package_Server_Settings_Terminal_User_Users_Wrench_X_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                        className: \"h-12 w-12 text-red-500\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 820,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-slate-900 font-medium\",\n                                                children: \"加载失败\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 822,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-slate-600 text-sm mt-1\",\n                                                children: [\n                                                    \"无法加载 \",\n                                                    selectedAgentData === null || selectedAgentData === void 0 ? void 0 : selectedAgentData.name\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 823,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 821,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        onClick: ()=>handleViewSelect(selectedView),\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        children: \"重试\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 825,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 819,\n                                columnNumber: 19\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 818,\n                            columnNumber: 17\n                        }, this) : selectedAgentData ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"iframe\", {\n                            src: selectedAgentData.url,\n                            className: \"w-full h-full border-0 rounded-lg\",\n                            title: selectedAgentData.name,\n                            onError: ()=>setIframeError(true),\n                            sandbox: \"allow-scripts allow-same-origin allow-forms\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 831,\n                            columnNumber: 17\n                        }, this) : null\n                    }, void 0, false, {\n                        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 809,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                    lineNumber: 808,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 655,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\my_project\\\\work\\\\hermes\\\\frontend\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 436,\n        columnNumber: 5\n    }, this);\n}\n_s(DashboardPage, \"WP+xSObwB6TJowbL3Zc/sms9bro=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = DashboardPage;\nvar _c;\n$RefreshReg$(_c, \"DashboardPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/dashboard/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/chevron-right.js":
/*!************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/chevron-right.js ***!
  \************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ChevronRight)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.454.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst ChevronRight = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"ChevronRight\", [\n    [\n        \"path\",\n        {\n            d: \"m9 18 6-6-6-6\",\n            key: \"mthhwq\"\n        }\n    ]\n]);\n //# sourceMappingURL=chevron-right.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9sdWNpZGUtcmVhY3RAMC40NTQuMF9yZWFjdEAxOS4xLjEvbm9kZV9tb2R1bGVzL2x1Y2lkZS1yZWFjdC9kaXN0L2VzbS9pY29ucy9jaGV2cm9uLXJpZ2h0LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBYU0scUJBQWUsZ0VBQWdCLENBQUMsY0FBZ0I7SUFDcEQ7UUFBQyxNQUFRO1FBQUE7WUFBRSxHQUFHLENBQWlCO1lBQUEsS0FBSztRQUFBLENBQVU7S0FBQTtDQUMvQyIsInNvdXJjZXMiOlsiRDpcXG15X3Byb2plY3RcXHNyY1xcaWNvbnNcXGNoZXZyb24tcmlnaHQudHMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGNyZWF0ZUx1Y2lkZUljb24gZnJvbSAnLi4vY3JlYXRlTHVjaWRlSWNvbic7XG5cbi8qKlxuICogQGNvbXBvbmVudCBAbmFtZSBDaGV2cm9uUmlnaHRcbiAqIEBkZXNjcmlwdGlvbiBMdWNpZGUgU1ZHIGljb24gY29tcG9uZW50LCByZW5kZXJzIFNWRyBFbGVtZW50IHdpdGggY2hpbGRyZW4uXG4gKlxuICogQHByZXZpZXcgIVtpbWddKGRhdGE6aW1hZ2Uvc3ZnK3htbDtiYXNlNjQsUEhOMlp5QWdlRzFzYm5NOUltaDBkSEE2THk5M2QzY3Vkek11YjNKbkx6SXdNREF2YzNabklnb2dJSGRwWkhSb1BTSXlOQ0lLSUNCb1pXbG5hSFE5SWpJMElnb2dJSFpwWlhkQ2IzZzlJakFnTUNBeU5DQXlOQ0lLSUNCbWFXeHNQU0p1YjI1bElnb2dJSE4wY205clpUMGlJekF3TUNJZ2MzUjViR1U5SW1KaFkydG5jbTkxYm1RdFkyOXNiM0k2SUNObVptWTdJR0p2Y21SbGNpMXlZV1JwZFhNNklESndlQ0lLSUNCemRISnZhMlV0ZDJsa2RHZzlJaklpQ2lBZ2MzUnliMnRsTFd4cGJtVmpZWEE5SW5KdmRXNWtJZ29nSUhOMGNtOXJaUzFzYVc1bGFtOXBiajBpY205MWJtUWlDajRLSUNBOGNHRjBhQ0JrUFNKdE9TQXhPQ0EyTFRZdE5pMDJJaUF2UGdvOEwzTjJaejRLKSAtIGh0dHBzOi8vbHVjaWRlLmRldi9pY29ucy9jaGV2cm9uLXJpZ2h0XG4gKiBAc2VlIGh0dHBzOi8vbHVjaWRlLmRldi9ndWlkZS9wYWNrYWdlcy9sdWNpZGUtcmVhY3QgLSBEb2N1bWVudGF0aW9uXG4gKlxuICogQHBhcmFtIHtPYmplY3R9IHByb3BzIC0gTHVjaWRlIGljb25zIHByb3BzIGFuZCBhbnkgdmFsaWQgU1ZHIGF0dHJpYnV0ZVxuICogQHJldHVybnMge0pTWC5FbGVtZW50fSBKU1ggRWxlbWVudFxuICpcbiAqL1xuY29uc3QgQ2hldnJvblJpZ2h0ID0gY3JlYXRlTHVjaWRlSWNvbignQ2hldnJvblJpZ2h0JywgW1xuICBbJ3BhdGgnLCB7IGQ6ICdtOSAxOCA2LTYtNi02Jywga2V5OiAnbXRoaHdxJyB9XSxcbl0pO1xuXG5leHBvcnQgZGVmYXVsdCBDaGV2cm9uUmlnaHQ7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.454.0_react@19.1.1/node_modules/lucide-react/dist/esm/icons/chevron-right.js\n"));

/***/ })

});